{"name": "econ", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@sisense/sdk-cli": "^2.1.1", "@sisense/sdk-data": "^2.1.1", "@sisense/sdk-ui": "^2.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "overrides": {"react-custom-scrollbars": {"react": "$react", "react-dom": "$react-dom"}}}