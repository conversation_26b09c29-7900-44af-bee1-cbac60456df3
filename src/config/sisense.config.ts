// Sisense configuration
// Authentication will be implemented later

export const sisenseConfig = {
  url: import.meta.env.VITE_SISENSE_URL || 'https://your-sisense-instance.com',
  defaultDataSource: import.meta.env.VITE_SISENSE_DATA_SOURCE || 'ARTBA Economics',
  // Authentication config will be added later
  // token: import.meta.env.VITE_SISENSE_TOKEN,
};

import { createAttribute } from '@sisense/sdk-data';

// ARTBA Economics data model
// Update these attributes to match your actual data model structure
export const DM = {
  ARTBAEconomics: {
    // Awards dimensions
    awards: {
      Year: createAttribute({
        name: 'Year',
        type: 'datetime',
        expression: '[ARTBA Economics.awards.year]',
      }),
      Value: createAttribute({
        name: 'Value',
        type: 'numeric',
        expression: '[ARTBA Economics.awards.value]',
      }),
      State: createAttribute({
        name: 'State',
        type: 'text',
        expression: '[ARTBA Economics.awards.state]',
      }),
      Mode: createAttribute({
        name: 'Mode',
        type: 'text',
        expression: '[ARTBA Economics.awards.mode]',
      }),
      ProjectCount: createAttribute({
        name: 'Project Count',
        type: 'numeric',
        expression: '[ARTBA Economics.awards.project_count]',
      }),
    },
    // Add other dimensions as needed
  },
};