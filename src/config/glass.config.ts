/**
 * Glass Effect Configuration
 * Defines presets and settings for liquid glass effects throughout the application
 */

export interface GlassEffectConfig {
  displacementScale: number;
  blurAmount: number;
  saturation: number;
  aberrationIntensity: number;
  elasticity: number;
  cornerRadius: number;
  opacity: number;
  backdropBlur: string;
  background: string;
  border: string;
  boxShadow: string;
}

export interface GlassPreset {
  name: string;
  description: string;
  config: GlassEffectConfig;
}

/**
 * Glass effect presets for different use cases
 */
export const glassPresets: Record<string, GlassPreset> = {
  subtle: {
    name: 'Subtle',
    description: 'Minimal glass effect for data-heavy widgets',
    config: {
      displacementScale: 25,
      blurAmount: 0.03,
      saturation: 115,
      aberrationIntensity: 0.5,
      elasticity: 0.08,
      cornerRadius: 12,
      opacity: 0.95,
      backdropBlur: 'blur(8px)',
      background: 'rgba(255, 255, 255, 0.1)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15)',
    },
  },
  medium: {
    name: 'Medium',
    description: 'Balanced glass effect for KPI widgets and headers',
    config: {
      displacementScale: 45,
      blurAmount: 0.06,
      saturation: 135,
      aberrationIntensity: 1.2,
      elasticity: 0.12,
      cornerRadius: 16,
      opacity: 0.9,
      backdropBlur: 'blur(12px)',
      background: 'rgba(255, 255, 255, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.2)',
    },
  },
  prominent: {
    name: 'Prominent',
    description: 'Strong glass effect for navigation and backgrounds',
    config: {
      displacementScale: 70,
      blurAmount: 0.12,
      saturation: 150,
      aberrationIntensity: 2,
      elasticity: 0.2,
      cornerRadius: 20,
      opacity: 0.85,
      backdropBlur: 'blur(16px)',
      background: 'rgba(255, 255, 255, 0.2)',
      border: '1px solid rgba(255, 255, 255, 0.3)',
      boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.25)',
    },
  },
  minimal: {
    name: 'Minimal',
    description: 'Very light glass effect for accessibility',
    config: {
      displacementScale: 15,
      blurAmount: 0.02,
      saturation: 105,
      aberrationIntensity: 0.3,
      elasticity: 0.05,
      cornerRadius: 8,
      opacity: 0.98,
      backdropBlur: 'blur(4px)',
      background: 'rgba(255, 255, 255, 0.05)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      boxShadow: '0 4px 16px 0 rgba(31, 38, 135, 0.1)',
    },
  },
};

/**
 * Widget-specific glass configurations
 */
export const widgetGlassConfig = {
  kpi: 'medium',
  chart: 'subtle',
  table: 'subtle',
  header: 'medium',
  navigation: 'prominent',
  background: 'minimal',
} as const;

/**
 * Browser capability detection
 */
export const browserSupport = {
  chrome: {
    displacement: true,
    backdropFilter: true,
    fullSupport: true,
  },
  firefox: {
    displacement: false,
    backdropFilter: true,
    fullSupport: false,
  },
  safari: {
    displacement: false,
    backdropFilter: true,
    fullSupport: false,
  },
  edge: {
    displacement: true,
    backdropFilter: true,
    fullSupport: true,
  },
} as const;

/**
 * Responsive glass configurations
 */
export const responsiveGlass = {
  mobile: {
    displacementScale: 0.7, // Reduce by 30%
    blurAmount: 0.8, // Reduce by 20%
    elasticity: 0.5, // Reduce by 50%
  },
  tablet: {
    displacementScale: 0.85, // Reduce by 15%
    blurAmount: 0.9, // Reduce by 10%
    elasticity: 0.75, // Reduce by 25%
  },
  desktop: {
    displacementScale: 1, // Full effect
    blurAmount: 1, // Full effect
    elasticity: 1, // Full effect
  },
} as const;

/**
 * Animation configurations for glass effects
 */
export const glassAnimations = {
  hover: {
    duration: '300ms',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    transform: 'translateY(-2px) scale(1.02)',
  },
  focus: {
    duration: '200ms',
    easing: 'ease-out',
    outline: '2px solid rgba(59, 130, 246, 0.5)',
    outlineOffset: '2px',
  },
  active: {
    duration: '150ms',
    easing: 'ease-in',
    transform: 'translateY(0px) scale(0.98)',
  },
} as const;

/**
 * Accessibility settings
 */
export const accessibilitySettings = {
  reducedMotion: {
    disableAnimations: true,
    staticEffects: true,
    fallbackToBasic: true,
  },
  highContrast: {
    increaseBorder: true,
    enhanceShadows: true,
    adjustOpacity: true,
  },
  colorBlind: {
    enhanceContrast: true,
    adjustSaturation: false,
  },
} as const;

/**
 * Performance settings
 */
export const performanceSettings = {
  enableGPUAcceleration: true,
  useWillChange: true,
  enableContainment: true,
  lazyLoad: true,
  debounceMs: 16, // 60fps
} as const;
