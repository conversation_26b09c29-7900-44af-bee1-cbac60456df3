import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Landing } from './pages/Landing/Landing';
import { SummaryDashboard } from './pages/dashboards/SummaryDashboard/SummaryDashboard';
import { ContractAwardsDashboard } from './pages/dashboards/ContractAwardsDashboard/ContractAwardsDashboard';
import { ValuePutInPlaceDashboard } from './pages/dashboards/ValuePutInPlaceDashboard/ValuePutInPlaceDashboard';
import { FederalAidDashboard } from './pages/dashboards/FederalAidDashboard/FederalAidDashboard';
import { MaterialPricesDashboard } from './pages/dashboards/MaterialPricesDashboard/MaterialPricesDashboard';
import { StateLegislativeInitiativesDashboard } from './pages/dashboards/StateLegislativeInitiativesDashboard/StateLegislativeInitiativesDashboard';
import { StateDOTBudgetsDashboard } from './pages/dashboards/StateDOTBudgetsDashboard/StateDOTBudgetsDashboard';
import { DashboardImporter } from './pages/DashboardImporter/DashboardImporter';
import './App.css';

const App: React.FC = () => {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Landing />} />
        <Route path="/import" element={<DashboardImporter />} />
        <Route path="/dashboard/summary" element={<SummaryDashboard />} />
        <Route path="/dashboard/contract-awards" element={<ContractAwardsDashboard />} />
        <Route path="/dashboard/value-put-in-place" element={<ValuePutInPlaceDashboard />} />
        <Route path="/dashboard/federal-aid" element={<FederalAidDashboard />} />
        <Route path="/dashboard/state-legislative-initiatives" element={<StateLegislativeInitiativesDashboard />} />
        <Route path="/dashboard/state-dot-budgets" element={<StateDOTBudgetsDashboard />} />
        <Route path="/dashboard/material-prices" element={<MaterialPricesDashboard />} />
      </Routes>
    </Router>
  );
};

export default App;
