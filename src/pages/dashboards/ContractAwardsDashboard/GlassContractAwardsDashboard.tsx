import React, { useState, useEffect } from 'react';
import { GlassPageLayout, GlassDashboardHeader } from '../../../components/layout';
import { DashboardActions } from '../../../components/layout/DashboardActions';
import { TabNavigation } from '../../../components/layout/TabNavigation';
import { GlassBackground, GlassBackgroundWithOrbs } from '../../../components/glass';
import { LoadingSpinner } from '../../../components/layout/LoadingSpinner';
import { ErrorBoundary, ErrorDisplay } from '../../../components/layout/ErrorBoundary';
import { theme } from '../../../config/theme.config';
import { glassUtils } from '../../../utils/glassUtils';
import { createGlassWidget } from '../../../utils/glassWidgetFactory';
import { contractAwardsDashboardConfig } from './contractAwardsDashboard.config';

/**
 * GlassContractAwardsDashboard - Enhanced Contract Awards Dashboard with glass effects
 * 
 * This component provides the same functionality as the original ContractAwardsDashboard
 * but with modern glassmorphism effects, while maintaining full compatibility with
 * Sisense widgets and existing functionality.
 */
export const GlassContractAwardsDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('MONTH');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [glassEffectsEnabled, setGlassEffectsEnabled] = useState<boolean>(true);
  
  const { id: dashboardId, widgets, title, description } = contractAwardsDashboardConfig;

  // Check if glass effects should be enabled based on browser capabilities and user preferences
  useEffect(() => {
    const shouldEnable = glassUtils.shouldEnableGlassEffects({
      respectPerformance: true,
      respectAccessibility: true,
      respectBrowserSupport: false, // Allow fallbacks for better compatibility
    });
    
    setGlassEffectsEnabled(shouldEnable);
  }, []);

  // Simulate loading dashboard data
  useEffect(() => {
    const loadDashboard = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        setIsLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        setIsLoading(false);
      }
    };

    loadDashboard();
  }, []);

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const handleExport = () => {
    console.log('Exporting Contract Awards Dashboard data...');
  };

  const handleRetry = () => {
    setError(null);
    setIsLoading(true);
    // Trigger reload
    window.location.reload();
  };

  const handleToggleGlassEffects = () => {
    setGlassEffectsEnabled(!glassEffectsEnabled);
  };

  // Filter widgets by type and visibility
  const kpiWidgets = widgets.filter(widget => 
    widget.type === 'kpi' && 
    (!widget.visibleOn || widget.visibleOn.includes(activeTab))
  );

  const tabSpecificWidgets = widgets.filter(widget => 
    widget.type !== 'kpi' && 
    (!widget.visibleOn || widget.visibleOn.includes(activeTab))
  );

  // Tab configuration
  const tabs = [
    { id: 'MONTH', label: 'Monthly View', icon: '📅' },
    { id: 'YTD', label: 'Year to Date', icon: '📊' },
    { id: 'TTM', label: 'Trailing 12 Months', icon: '📈' },
    { id: 'GEOGRAPHIC', label: 'Geographic Analysis', icon: '🗺️' },
  ];

  // Enhanced dashboard actions with glass toggle
  const enhancedActions = (
    <div style={{ display: 'flex', gap: theme.spacing.md, alignItems: 'center' }}>
      <button
        onClick={handleToggleGlassEffects}
        style={{
          padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
          borderRadius: theme.borderRadius.md,
          border: `1px solid ${theme.colors.border.light}`,
          background: glassEffectsEnabled 
            ? theme.colors.gradients.primary 
            : theme.colors.surface,
          color: glassEffectsEnabled ? 'white' : theme.colors.text.primary,
          fontSize: theme.typography.fontSize.sm,
          fontWeight: theme.typography.fontWeight.medium,
          cursor: 'pointer',
          transition: theme.transitions.fast,
        }}
        title={glassEffectsEnabled ? 'Disable Glass Effects' : 'Enable Glass Effects'}
      >
        {glassEffectsEnabled ? '✨ Glass On' : '🔲 Glass Off'}
      </button>
      <DashboardActions onExport={handleExport} />
    </div>
  );

  // Loading state
  if (isLoading) {
    return (
      <GlassPageLayout 
        title="Contract Awards Dashboard" 
        enableGlassEffects={glassEffectsEnabled}
        backgroundVariant="gradient"
      >
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '60vh',
          gap: theme.spacing.lg,
        }}>
          <LoadingSpinner size="large" />
          <div style={{
            color: theme.colors.text.secondary,
            fontSize: theme.typography.fontSize.lg,
            textAlign: 'center',
          }}>
            Loading Contract Awards Dashboard...
            <br />
            <small style={{ fontSize: theme.typography.fontSize.sm }}>
              {glassEffectsEnabled ? 'Glass effects enabled' : 'Glass effects disabled'}
            </small>
          </div>
        </div>
      </GlassPageLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <GlassPageLayout 
        title="Contract Awards Dashboard" 
        enableGlassEffects={glassEffectsEnabled}
        backgroundVariant="subtle"
      >
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: theme.spacing.xl,
        }}>
          <ErrorDisplay
            title="Dashboard Loading Error"
            message={error}
            onRetry={handleRetry}
            icon="📊"
          />
        </div>
      </GlassPageLayout>
    );
  }

  return (
    <ErrorBoundary>
      <GlassPageLayout 
        title="Contract Awards Dashboard"
        enableGlassEffects={glassEffectsEnabled}
        backgroundVariant="mesh"
        headerGlassPreset="medium"
      >
        {/* Enhanced background with orbs for visual interest */}
        {glassEffectsEnabled && (
          <GlassBackgroundWithOrbs
            variant="gradient"
            opacity={0.3}
            animated={true}
            orbCount={3}
            orbSizes={[180, 220, 160]}
            orbColors={[
              'rgba(37, 99, 235, 0.06)',
              'rgba(6, 182, 212, 0.04)',
              'rgba(59, 130, 246, 0.05)',
            ]}
          />
        )}

        <div style={{
          minHeight: '100vh',
          padding: theme.spacing.xl,
          position: 'relative',
          zIndex: 1,
        }}>
          {/* Dashboard Header with Glass Effects */}
          <GlassDashboardHeader
            title={title}
            description={description}
            actions={enhancedActions}
            enableGlassEffects={glassEffectsEnabled}
            glassPreset="medium"
          />

          {/* KPI Section - Always visible widgets */}
          <div style={{
            display: 'flex',
            flexDirection: 'row',
            gap: theme.spacing.lg,
            marginBottom: theme.spacing.xl,
            width: '100%',
            maxWidth: '1400px',
            margin: '0 auto',
            flexWrap: 'wrap',
            justifyContent: 'center',
          }}>
            {kpiWidgets.map(widget => (
              <div 
                key={widget.id} 
                style={{ 
                  flex: '1 1 300px', 
                  minWidth: '300px', 
                  maxWidth: '400px',
                  animation: 'fadeInUp 0.6s ease-out',
                  animationDelay: `${kpiWidgets.indexOf(widget) * 0.1}s`,
                  animationFillMode: 'both',
                }}
              >
                {createGlassWidget(widget, dashboardId, activeTab, {
                  enableGlassEffects: glassEffectsEnabled,
                  glassPreset: 'medium',
                  widgetType: 'kpi',
                })}
              </div>
            ))}
          </div>

          {/* Tab Navigation with Glass Enhancement */}
          <div style={{ 
            marginBottom: theme.spacing.xl,
            display: 'flex',
            justifyContent: 'center',
          }}>
            <TabNavigation
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={handleTabChange}
              variant="pills"
              size="md"
            />
          </div>

          {/* Tab-specific widgets with staggered animation */}
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(12, 1fr)',
              gap: theme.spacing.lg,
              gridAutoRows: 'minmax(120px, auto)',
              width: '100%',
              maxWidth: '1400px',
              margin: '0 auto',
            }}
          >
            {tabSpecificWidgets.map((widget, index) => (
              <div
                key={`${widget.id}-${activeTab}`}
                style={{
                  animation: 'fadeInUp 0.6s ease-out',
                  animationDelay: `${index * 0.05}s`,
                  animationFillMode: 'both',
                }}
              >
                {createGlassWidget(widget, dashboardId, activeTab, {
                  enableGlassEffects: glassEffectsEnabled,
                  glassPreset: 'subtle',
                  widgetType: widget.type === 'chart' ? 'chart' : 'table',
                })}
              </div>
            ))}
          </div>
        </div>
      </GlassPageLayout>
    </ErrorBoundary>
  );
};
