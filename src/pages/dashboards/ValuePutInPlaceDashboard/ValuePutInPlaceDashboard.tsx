import React from 'react';
import { useNavigate } from 'react-router-dom';
import { PageLayout } from '../../../components/layout/PageLayout';
import { DashboardLayout } from '../../../components/layout/DashboardLayout';
import { theme } from '../../../config/theme.config';
import { createWidget } from '../../../utils/widgetFactory';
import { valuePutInPlaceDashboardConfig } from './valuePutInPlaceDashboard.config';

export const ValuePutInPlaceDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { id: dashboardId, widgets, title, description } = valuePutInPlaceDashboardConfig;

  const dashboardActions = (
    <>
      <button
        onClick={() => navigate('/')}
        style={{
          backgroundColor: 'transparent',
          color: theme.colors.primary,
          border: `1px solid ${theme.colors.primary}`,
          borderRadius: theme.borderRadius.md,
          padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
          fontSize: '14px',
          fontWeight: 500,
          cursor: 'pointer',
          transition: `all ${theme.transitions.fast}`,
        }}
      >
        ← Back to Home
      </button>
      <button
        style={{
          backgroundColor: theme.colors.primary,
          color: 'white',
          border: 'none',
          borderRadius: theme.borderRadius.md,
          padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
          fontSize: '14px',
          fontWeight: 500,
          cursor: 'pointer',
          transition: `background-color ${theme.transitions.fast}`,
        }}
      >
        Export Report
      </button>
    </>
  );

  return (
    <PageLayout title="Value Put in Place">
      <DashboardLayout
        title={title}
        description={description}
        actions={dashboardActions}
      >
        {widgets.map(widget => createWidget(widget, dashboardId))}
      </DashboardLayout>
    </PageLayout>
  );
};