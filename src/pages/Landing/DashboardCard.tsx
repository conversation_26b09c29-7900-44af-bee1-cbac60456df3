import React from 'react';
import { useNavigate } from 'react-router-dom';
import { theme } from '../../config/theme.config';

interface DashboardCardProps {
  title: string;
  description: string;
  path: string;
  icon?: string;
  color?: string;
}

export const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  description,
  path,
  icon,
  color = theme.colors.primary,
}) => {
  const navigate = useNavigate();

  return (
    <div
      onClick={() => navigate(path)}
      style={{
        background: theme.colors.surface,
        borderRadius: theme.borderRadius.card,
        boxShadow: theme.shadows.card,
        padding: theme.spacing.xl,
        cursor: 'pointer',
        transition: theme.transitions.normal,
        border: `1px solid ${theme.colors.border.light}`,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        overflow: 'hidden',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.cardHover;
        e.currentTarget.style.borderColor = color;
        e.currentTarget.style.transform = 'translateY(-6px) scale(1.02)';

        // Animate the arrow
        const arrow = e.currentTarget.querySelector('.card-arrow') as HTMLElement;
        if (arrow) {
          arrow.style.transform = 'translateX(4px)';
        }
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.card;
        e.currentTarget.style.borderColor = theme.colors.border.light;
        e.currentTarget.style.transform = 'translateY(0) scale(1)';

        // Reset arrow animation
        const arrow = e.currentTarget.querySelector('.card-arrow') as HTMLElement;
        if (arrow) {
          arrow.style.transform = 'translateX(0)';
        }
      }}
    >
      {/* Background gradient accent */}
      <div style={{
        position: 'absolute',
        top: 0,
        right: 0,
        width: '100px',
        height: '100px',
        background: `linear-gradient(135deg, ${color}20, transparent)`,
        borderRadius: '0 0 0 100px',
        pointerEvents: 'none',
      }} />

      <div style={{
        display: 'flex',
        alignItems: 'flex-start',
        gap: theme.spacing.lg,
        marginBottom: theme.spacing.lg,
        position: 'relative',
        zIndex: 1,
      }}>
        {icon && (
          <div style={{
            width: 56,
            height: 56,
            background: `linear-gradient(135deg, ${color}, ${color}dd)`,
            borderRadius: theme.borderRadius.lg,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '28px',
            color: 'white',
            boxShadow: `0 4px 12px ${color}40`,
            flexShrink: 0,
          }}>
            {icon}
          </div>
        )}
        <div style={{ flex: 1 }}>
          <h3 style={{
            margin: 0,
            color: theme.colors.text.primary,
            fontSize: theme.typography.fontSize.xl,
            fontWeight: theme.typography.fontWeight.semibold,
            lineHeight: theme.typography.lineHeight.tight,
            marginBottom: theme.spacing.xs,
          }}>
            {title}
          </h3>
        </div>
      </div>
      <p style={{
        margin: 0,
        color: theme.colors.text.secondary,
        fontSize: theme.typography.fontSize.base,
        lineHeight: theme.typography.lineHeight.relaxed,
        flex: 1,
        marginBottom: theme.spacing.lg,
      }}>
        {description}
      </p>

      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: 'auto',
      }}>
        <div style={{
          color: color,
          fontSize: theme.typography.fontSize.sm,
          fontWeight: theme.typography.fontWeight.semibold,
          display: 'flex',
          alignItems: 'center',
          gap: theme.spacing.sm,
          transition: theme.transitions.fast,
        }}>
          Open Dashboard
          <span
            className="card-arrow"
            style={{
              fontSize: '16px',
              transition: theme.transitions.normal,
              display: 'inline-block',
            }}
          >
            →
          </span>
        </div>

        {/* Status indicator */}
        <div style={{
          width: 8,
          height: 8,
          borderRadius: '50%',
          backgroundColor: theme.colors.success,
          boxShadow: `0 0 0 2px ${theme.colors.success}20`,
        }} />
      </div>
    </div>
  );
};