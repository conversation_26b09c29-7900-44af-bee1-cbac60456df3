import React from 'react';
import { useNavigate } from 'react-router-dom';
import { PageLayout } from '../../components/layout/PageLayout';
import { DashboardCard } from './DashboardCard';
import { createWidget } from '../../utils/widgetFactory';
import { theme } from '../../config/theme.config';
import type { WidgetConfig } from '../../types/dashboard.types';

// These dashboards are extracted from the .dash files
const dashboards = [
  {
    id: 'summary',
    title: '1. Summary Dashboard',
    description: 'Executive summary of key transportation metrics including contract awards, federal obligations, and material prices.',
    path: '/dashboard/summary',
    icon: '📊',
    color: theme.colors.primary,
  },
  {
    id: 'contract-awards',
    title: '2. Contract Awards',
    description: 'Track state and local contract awards with TTM and YTD metrics, geographic distribution, and trending analysis.',
    path: '/dashboard/contract-awards',
    icon: '📄',
    color: '#00cee6',
  },
  {
    id: 'contract-awards-glass',
    title: '2b. Contract Awards (Glass)',
    description: 'Enhanced Contract Awards dashboard with modern liquid glass effects and glassmorphism design.',
    path: '/dashboard/contract-awards-glass',
    icon: '✨',
    color: '#6366f1',
  },
  {
    id: 'value-put-in-place',
    title: '3. Value Put in Place',
    description: 'Monitor construction value put in place across highways, bridges, and other infrastructure projects.',
    path: '/dashboard/value-put-in-place',
    icon: '🏗️',
    color: '#9b9bd7',
  },
  {
    id: 'federal-aid',
    title: '4. Federal-Aid Obligations',
    description: 'Track federal highway aid obligations by state and program, with historical trends and distributions.',
    path: '/dashboard/federal-aid',
    icon: '🏛️',
    color: '#6EDA55',
  },
  {
    id: 'state-legislative',
    title: '6. State Legislative Initiatives',
    description: 'Monitor state transportation funding initiatives, ballot measures, and legislative actions.',
    path: '/dashboard/state-legislative-initiatives',
    icon: '📜',
    color: '#fc7570',
  },
  {
    id: 'state-dot-budgets',
    title: '7. State DOT Budgets 2025',
    description: 'Analyze state department of transportation budgets, funding sources, and year-over-year changes.',
    path: '/dashboard/state-dot-budgets',
    icon: '💰',
    color: '#fbb755',
  },
  {
    id: 'material-prices',
    title: '9. Material Prices',
    description: 'Track construction material price indices for asphalt, concrete, steel, and aggregates with trend analysis.',
    path: '/dashboard/material-prices',
    icon: '📈',
    color: '#218A8C',
  },
];

// Widget configurations based on user specifications
const landingWidgets: WidgetConfig[] = [
  // Row 1 - KPI Widgets
  {
    id: '6865541f099a11833ea60af0',
    type: 'kpi',
    title: 'TTM Contract Awards',
    position: { x: 0, y: 0, w: 4, h: 3 },
    config: {
      type: 'indicator',
      description: 'Displays TTM contract awards value and percentage change from previous year',
    },
  },
  {
    id: '6865541f099a11833ea60af2',
    type: 'kpi',
    title: 'TTM Construction Activity',
    position: { x: 4, y: 0, w: 4, h: 3 },
    config: {
      type: 'indicator',
      description: 'Displays TTM construction work value and percentage change from previous year',
    },
  },
  {
    id: '6865541f099a11833ea60af3',
    type: 'kpi',
    title: 'FY 2025 Formula Funds Committed',
    position: { x: 8, y: 0, w: 4, h: 3 },
    config: {
      type: 'gauge',
      description: 'Gauge showing percentage of federal formula funds committed (22/47 = 46.8%)',
    },
  },
  // Row 2 - Main Charts
  {
    id: '6865541f099a11833ea60af6',
    type: 'chart',
    title: 'ARTBA 2025 Transp. Const. Market Outlook',
    position: { x: 0, y: 3, w: 6, h: 4 },
    config: {
      type: 'stacked_column',
      description: 'Stacked column chart showing construction activity from 2019-2025 by type',
    },
  },
  {
    id: '6865541f099a11833ea60af7',
    type: 'chart',
    title: 'U.S. Transp. Const. Work by Mode',
    position: { x: 6, y: 3, w: 6, h: 4 },
    config: {
      type: 'donut',
      description: 'Donut chart showing TTM construction work breakdown by transportation mode',
    },
  },
  // Row 3 - Time Series Charts
  {
    id: '6865541f099a11833ea60af4',
    type: 'chart',
    title: 'TTM U.S. Transp. Const. Work',
    position: { x: 0, y: 7, w: 6, h: 4 },
    config: {
      type: 'column',
      description: 'Time-series column chart showing TTM transportation construction work from 2021',
    },
  },
  {
    id: '6865541f099a11833ea60b03',
    type: 'chart',
    title: 'TTM State & Local Govt. Transp. Contract Awards',
    position: { x: 6, y: 7, w: 6, h: 4 },
    config: {
      type: 'column',
      description: 'Time-series column chart showing TTM contract awards from 2021',
    },
  },
  // Row 4 - Ballot Measures Chart
  {
    id: '6865541f099a11833ea60b04',
    type: 'chart',
    title: 'General Election Transportation Funding Ballot Measures, 2014 - 2024',
    position: { x: 0, y: 11, w: 12, h: 4 },
    config: {
      type: 'combination',
      description: 'Combination chart with columns (number of measures) and line (approval percentage)',
    },
  },
  // Row 5 - State Legislative Map
  {
    id: '6865541f099a11833ea60afd',
    type: 'chart',
    title: 'Number of State Legislative Funding Measures Introduced in 2025',
    position: { x: 0, y: 15, w: 12, h: 5 },
    config: {
      type: 'map',
      description: 'Area map of US states colored by number of transportation funding bills introduced',
    },
  },
];

export const Landing: React.FC = () => {
  const navigate = useNavigate();

  return (
    <PageLayout title="Analytics Dashboard">
      <div className="animate-fade-in">
        {/* Hero Section */}
        <div style={{
          textAlign: 'center',
          marginBottom: theme.spacing.xl,
          padding: `${theme.spacing.xxl}px 0`,
          background: theme.colors.gradients.surface,
          borderRadius: theme.borderRadius.xxl,
          position: 'relative',
          overflow: 'hidden',
        }}>
          {/* Background decoration */}
          <div style={{
            position: 'absolute',
            top: '-50%',
            left: '-50%',
            width: '200%',
            height: '200%',
            background: 'radial-gradient(circle, rgba(37, 99, 235, 0.05) 0%, transparent 70%)',
            pointerEvents: 'none',
          }} />

          <div style={{ position: 'relative', zIndex: 1 }}>
            <h1 className="gradient-text" style={{
              fontSize: theme.typography.fontSize.xxxl,
              fontWeight: theme.typography.fontWeight.bold,
              margin: 0,
              marginBottom: theme.spacing.lg,
              lineHeight: theme.typography.lineHeight.tight,
            }}>
              ARTBA Economics Dashboard
            </h1>
            <p style={{
              fontSize: theme.typography.fontSize.xl,
              color: theme.colors.text.secondary,
              maxWidth: '700px',
              margin: '0 auto',
              lineHeight: theme.typography.lineHeight.relaxed,
              fontWeight: theme.typography.fontWeight.medium,
            }}>
              Transportation construction industry analytics and economic indicators
            </p>

            {/* Stats or badges */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: theme.spacing.xl,
              marginTop: theme.spacing.xl,
              flexWrap: 'wrap',
            }}>
              {[
                { label: 'Dashboards', value: '7' },
                { label: 'Data Sources', value: '15+' },
                { label: 'Real-time', value: '24/7' },
              ].map((stat, index) => (
                <div key={index} style={{
                  textAlign: 'center',
                  padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
                  background: 'rgba(255, 255, 255, 0.8)',
                  borderRadius: theme.borderRadius.lg,
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${theme.colors.border.light}`,
                }}>
                  <div style={{
                    fontSize: theme.typography.fontSize.xl,
                    fontWeight: theme.typography.fontWeight.bold,
                    color: theme.colors.primary,
                    marginBottom: theme.spacing.xs,
                  }}>
                    {stat.value}
                  </div>
                  <div style={{
                    fontSize: theme.typography.fontSize.sm,
                    color: theme.colors.text.secondary,
                    fontWeight: theme.typography.fontWeight.medium,
                  }}>
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Key Metrics Section */}
        <div style={{ marginBottom: theme.spacing.section }}>
          <div style={{
            textAlign: 'center',
            marginBottom: theme.spacing.xl,
          }}>
            <h2 style={{
              fontSize: theme.typography.fontSize.xxl,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.md,
            }}>
              Key Transportation Metrics
            </h2>
            <p style={{
              fontSize: theme.typography.fontSize.lg,
              color: theme.colors.text.secondary,
              maxWidth: '600px',
              margin: '0 auto',
            }}>
              Real-time insights into transportation construction activity and funding
            </p>
          </div>

          {/* Widgets Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(12, 1fr)',
            gap: theme.spacing.lg,
            marginBottom: theme.spacing.xl,
          }}>
            {landingWidgets.map((widget, index) => (
              <div
                key={widget.id}
                className="animate-slide-up"
                style={{
                  gridColumn: `span ${widget.position.w}`,
                  minHeight: `${widget.position.h * 60}px`,
                  animationDelay: `${index * 100}ms`,
                }}
              >
                {createWidget(widget, 'landing-dashboard')}
              </div>
            ))}
          </div>
        </div>

        {/* Dashboard Grid */}
        <div style={{ marginBottom: theme.spacing.section }}>
          <div style={{
            textAlign: 'center',
            marginBottom: theme.spacing.xl,
          }}>
            <h2 style={{
              fontSize: theme.typography.fontSize.xxl,
              fontWeight: theme.typography.fontWeight.semibold,
              color: theme.colors.text.primary,
              marginBottom: theme.spacing.md,
            }}>
              Explore Dashboards
            </h2>
            <p style={{
              fontSize: theme.typography.fontSize.lg,
              color: theme.colors.text.secondary,
              maxWidth: '600px',
              margin: '0 auto',
            }}>
              Dive into comprehensive analytics across different aspects of transportation construction
            </p>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
            gap: theme.spacing.xl,
            marginBottom: theme.spacing.xl,
          }}>
            {dashboards.map((dashboard, index) => (
              <div
                key={dashboard.id}
                className="animate-slide-up"
                style={{
                  animationDelay: `${index * 100}ms`,
                }}
              >
                <DashboardCard
                  title={dashboard.title}
                  description={dashboard.description}
                  path={dashboard.path}
                  icon={dashboard.icon}
                  color={dashboard.color}
                />
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div style={{
          background: theme.colors.gradients.primary,
          borderRadius: theme.borderRadius.xxl,
          padding: theme.spacing.xxl,
          textAlign: 'center',
          marginTop: theme.spacing.section,
          position: 'relative',
          overflow: 'hidden',
        }}>
          {/* Background decoration */}
          <div style={{
            position: 'absolute',
            top: '-20%',
            right: '-20%',
            width: '40%',
            height: '140%',
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '50%',
            pointerEvents: 'none',
          }} />

          <div style={{ position: 'relative', zIndex: 1 }}>
            <h2 style={{
              fontSize: theme.typography.fontSize.xxl,
              fontWeight: theme.typography.fontWeight.bold,
              color: theme.colors.text.inverse,
              marginBottom: theme.spacing.lg,
            }}>
              Ready to Get Started?
            </h2>
            <p style={{
              color: 'rgba(255, 255, 255, 0.9)',
              marginBottom: theme.spacing.xl,
              fontSize: theme.typography.fontSize.lg,
              maxWidth: '500px',
              margin: `0 auto ${theme.spacing.xl}px auto`,
            }}>
              Import your own dashboards or explore our comprehensive documentation
            </p>
            <div style={{
              display: 'flex',
              gap: theme.spacing.lg,
              justifyContent: 'center',
              flexWrap: 'wrap',
            }}>
              <button
                onClick={() => navigate('/import')}
                style={{
                  background: theme.colors.surface,
                  color: theme.colors.primary,
                  border: 'none',
                  borderRadius: theme.borderRadius.lg,
                  padding: `${theme.spacing.md}px ${theme.spacing.xl}px`,
                  fontSize: theme.typography.fontSize.md,
                  fontWeight: theme.typography.fontWeight.semibold,
                  cursor: 'pointer',
                  transition: theme.transitions.normal,
                  boxShadow: theme.shadows.md,
                }}
              >
                Import Dashboard
              </button>
              <button style={{
                background: 'rgba(255, 255, 255, 0.2)',
                color: theme.colors.text.inverse,
                border: `2px solid rgba(255, 255, 255, 0.3)`,
                borderRadius: theme.borderRadius.lg,
                padding: `${theme.spacing.md}px ${theme.spacing.xl}px`,
                fontSize: theme.typography.fontSize.md,
                fontWeight: theme.typography.fontWeight.semibold,
                cursor: 'pointer',
                transition: theme.transitions.normal,
                backdropFilter: 'blur(10px)',
              }}>
                View Documentation
              </button>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};