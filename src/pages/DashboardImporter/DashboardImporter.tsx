import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { PageLayout } from '../../components/layout/PageLayout';
import { theme } from '../../config/theme.config';
import { DashParserService } from '../../services/dashParser.service';
import type { DashFile } from '../../types/dash.types';
import type { DashboardConfig } from '../../types/dashboard.types';

export const DashboardImporter: React.FC = () => {
  const navigate = useNavigate();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [parsedDash, setParsedDash] = useState<DashFile | null>(null);
  const [dashboardConfig, setDashboardConfig] = useState<DashboardConfig | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.name.endsWith('.dash')) {
      setSelectedFile(file);
      setError(null);
      parseDashFile(file);
    } else {
      setError('Please select a valid .dash file');
    }
  };

  const parseDashFile = async (file: File) => {
    try {
      const content = await file.text();
      const dash = DashParserService.parseDashFile(content);
      setParsedDash(dash);
      
      const config = DashParserService.convertToDashboardConfig(dash);
      setDashboardConfig(config);
    } catch (err) {
      setError(`Failed to parse file: ${err}`);
    }
  };

  const handleImport = () => {
    if (dashboardConfig) {
      // TODO: Save the dashboard config and navigate to it
      console.log('Importing dashboard:', dashboardConfig);
      // For now, just navigate to home
      navigate('/');
    }
  };

  return (
    <PageLayout title="Import Dashboard">
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <div style={{
          backgroundColor: theme.colors.surface,
          borderRadius: theme.borderRadius.card,
          padding: theme.spacing.xl,
          boxShadow: theme.shadows.widget,
        }}>
          <h2 style={{
            margin: `0 0 ${theme.spacing.lg}px 0`,
            color: theme.colors.text.primary,
          }}>
            Import Sisense Dashboard
          </h2>

          <div style={{
            border: `2px dashed ${theme.colors.primary}`,
            borderRadius: theme.borderRadius.md,
            padding: theme.spacing.xl,
            textAlign: 'center',
            backgroundColor: `${theme.colors.primary}10`,
          }}>
            <input
              type="file"
              accept=".dash"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
              id="dash-file-input"
            />
            <label
              htmlFor="dash-file-input"
              style={{
                display: 'inline-block',
                padding: `${theme.spacing.md}px ${theme.spacing.xl}px`,
                backgroundColor: theme.colors.primary,
                color: 'white',
                borderRadius: theme.borderRadius.md,
                cursor: 'pointer',
                fontWeight: 500,
                transition: `background-color ${theme.transitions.fast}`,
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = theme.colors.primaryDark;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = theme.colors.primary;
              }}
            >
              Select .dash File
            </label>
            <p style={{
              margin: `${theme.spacing.md}px 0 0 0`,
              color: theme.colors.text.secondary,
            }}>
              Choose a Sisense dashboard export file (.dash)
            </p>
          </div>

          {error && (
            <div style={{
              marginTop: theme.spacing.md,
              padding: theme.spacing.md,
              backgroundColor: `${theme.colors.error}20`,
              borderRadius: theme.borderRadius.sm,
              color: theme.colors.error,
            }}>
              {error}
            </div>
          )}

          {parsedDash && (
            <div style={{ marginTop: theme.spacing.xl }}>
              <h3 style={{
                margin: `0 0 ${theme.spacing.md}px 0`,
                color: theme.colors.text.primary,
              }}>
                Dashboard Preview
              </h3>
              
              <div style={{
                backgroundColor: theme.colors.background,
                borderRadius: theme.borderRadius.md,
                padding: theme.spacing.md,
              }}>
                <p><strong>Title:</strong> {parsedDash.title}</p>
                <p><strong>Description:</strong> {parsedDash.desc || 'No description'}</p>
                <p><strong>Data Source:</strong> {parsedDash.datasource?.title || 'Unknown'}</p>
                <p><strong>Widgets:</strong> {parsedDash.widgets.length}</p>
              </div>

              {dashboardConfig && (
                <div style={{ marginTop: theme.spacing.md }}>
                  <h4>Converted Configuration:</h4>
                  <div style={{
                    backgroundColor: theme.colors.background,
                    borderRadius: theme.borderRadius.md,
                    padding: theme.spacing.md,
                    fontSize: '14px',
                  }}>
                    <p><strong>Dashboard ID:</strong> {dashboardConfig.id}</p>
                    <p><strong>Path:</strong> {dashboardConfig.path}</p>
                    <p><strong>Widgets Converted:</strong> {dashboardConfig.widgets.length}</p>
                    
                    <details style={{ marginTop: theme.spacing.sm }}>
                      <summary style={{ cursor: 'pointer', color: theme.colors.primary }}>
                        Widget Details
                      </summary>
                      <ul style={{ margin: `${theme.spacing.sm}px 0`, paddingLeft: theme.spacing.lg }}>
                        {dashboardConfig.widgets.map((widget) => (
                          <li key={widget.id}>
                            {widget.title || 'Untitled'} - Type: {widget.type}
                          </li>
                        ))}
                      </ul>
                    </details>
                  </div>
                </div>
              )}

              <div style={{
                marginTop: theme.spacing.xl,
                display: 'flex',
                gap: theme.spacing.md,
                justifyContent: 'flex-end',
              }}>
                <button
                  onClick={() => navigate('/')}
                  style={{
                    padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                    backgroundColor: 'transparent',
                    color: theme.colors.text.secondary,
                    border: `1px solid ${theme.colors.text.secondary}`,
                    borderRadius: theme.borderRadius.md,
                    cursor: 'pointer',
                    fontWeight: 500,
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleImport}
                  style={{
                    padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                    backgroundColor: theme.colors.primary,
                    color: 'white',
                    border: 'none',
                    borderRadius: theme.borderRadius.md,
                    cursor: 'pointer',
                    fontWeight: 500,
                  }}
                >
                  Import Dashboard
                </button>
              </div>
            </div>
          )}
        </div>

        <div style={{
          marginTop: theme.spacing.xl,
          backgroundColor: theme.colors.surface,
          borderRadius: theme.borderRadius.card,
          padding: theme.spacing.xl,
          boxShadow: theme.shadows.widget,
        }}>
          <h3 style={{
            margin: `0 0 ${theme.spacing.md}px 0`,
            color: theme.colors.text.primary,
          }}>
            Available Dashboards
          </h3>
          <p style={{
            color: theme.colors.text.secondary,
            marginBottom: theme.spacing.md,
          }}>
            The following .dash files are available in your project:
          </p>
          <ul style={{
            margin: 0,
            paddingLeft: theme.spacing.lg,
            color: theme.colors.text.secondary,
          }}>
            <li>SummaryDashboard.dash</li>
            <li>2ContractAwards.dash</li>
            <li>3ValuePutinPlace.dash</li>
            <li>4Federal-AidObligations.dash</li>
            <li>6StateLegislativeInitiatives.dash</li>
            <li>7StateDOTBudgets_2025.dash</li>
            <li>9MaterialPrices.dash</li>
          </ul>
        </div>
      </div>
    </PageLayout>
  );
};