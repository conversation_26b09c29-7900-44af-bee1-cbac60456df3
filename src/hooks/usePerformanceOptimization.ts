import { useState, useEffect, useCallback, useRef } from 'react';
import { glassUtils } from '../utils/glassUtils';

/**
 * Performance optimization hook for glass effects
 */
export const usePerformanceOptimization = () => {
  const [isLowPerformance, setIsLowPerformance] = useState(false);
  const [shouldReduceEffects, setShouldReduceEffects] = useState(false);
  const frameRef = useRef<number>();

  useEffect(() => {
    // Check device performance capabilities
    const checkPerformance = () => {
      const isLowPerf = glassUtils.device.isLowPerformanceDevice();
      const prefersReduced = glassUtils.accessibility.prefersReducedMotion();
      
      setIsLowPerformance(isLowPerf);
      setShouldReduceEffects(isLowPerf || prefersReduced);
    };

    checkPerformance();

    // Listen for media query changes
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    mediaQuery.addEventListener('change', checkPerformance);

    return () => {
      mediaQuery.removeEventListener('change', checkPerformance);
    };
  }, []);

  const requestOptimizedFrame = useCallback((callback: FrameRequestCallback) => {
    if (frameRef.current) {
      cancelAnimationFrame(frameRef.current);
    }
    frameRef.current = requestAnimationFrame(callback);
    return frameRef.current;
  }, []);

  const cancelOptimizedFrame = useCallback(() => {
    if (frameRef.current) {
      cancelAnimationFrame(frameRef.current);
      frameRef.current = undefined;
    }
  }, []);

  return {
    isLowPerformance,
    shouldReduceEffects,
    requestOptimizedFrame,
    cancelOptimizedFrame,
  };
};

/**
 * Intersection Observer hook for lazy loading glass effects
 */
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const targetRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const target = targetRef.current;
    if (!target) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(target);

    return () => {
      observer.unobserve(target);
    };
  }, [hasIntersected, options]);

  return {
    targetRef,
    isIntersecting,
    hasIntersected,
  };
};

/**
 * Debounced resize hook for responsive glass effects
 */
export const useDebouncedResize = (delay: number = 250) => {
  const [dimensions, setDimensions] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setDimensions({
          width: window.innerWidth,
          height: window.innerHeight,
        });
      }, delay);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timeoutId);
    };
  }, [delay]);

  return dimensions;
};

/**
 * Memory usage monitoring hook
 */
export const useMemoryMonitor = () => {
  const [memoryInfo, setMemoryInfo] = useState<{
    usedJSHeapSize?: number;
    totalJSHeapSize?: number;
    jsHeapSizeLimit?: number;
    isHighUsage: boolean;
  }>({ isHighUsage: false });

  useEffect(() => {
    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
        
        setMemoryInfo({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
          isHighUsage: usageRatio > 0.8, // Consider high usage if over 80%
        });
      }
    };

    // Check memory usage periodically
    const interval = setInterval(checkMemory, 5000);
    checkMemory(); // Initial check

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
};

/**
 * FPS monitoring hook
 */
export const useFPSMonitor = () => {
  const [fps, setFps] = useState(60);
  const [isLowFPS, setIsLowFPS] = useState(false);
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());

  useEffect(() => {
    let animationId: number;

    const measureFPS = () => {
      frameCount.current++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime.current + 1000) {
        const currentFPS = Math.round(
          (frameCount.current * 1000) / (currentTime - lastTime.current)
        );
        
        setFps(currentFPS);
        setIsLowFPS(currentFPS < 30); // Consider low FPS if below 30
        
        frameCount.current = 0;
        lastTime.current = currentTime;
      }
      
      animationId = requestAnimationFrame(measureFPS);
    };

    animationId = requestAnimationFrame(measureFPS);

    return () => {
      cancelAnimationFrame(animationId);
    };
  }, []);

  return { fps, isLowFPS };
};

/**
 * Comprehensive performance monitoring hook
 */
export const useGlassPerformanceMonitor = () => {
  const { isLowPerformance, shouldReduceEffects } = usePerformanceOptimization();
  const { isHighUsage } = useMemoryMonitor();
  const { isLowFPS } = useFPSMonitor();
  const dimensions = useDebouncedResize();

  const shouldDisableGlassEffects = isLowPerformance || isHighUsage || isLowFPS;
  const shouldUseMinimalEffects = shouldReduceEffects || shouldDisableGlassEffects;

  const getOptimalGlassPreset = useCallback(() => {
    if (shouldDisableGlassEffects) return 'minimal';
    if (shouldUseMinimalEffects) return 'subtle';
    if (dimensions.width < 768) return 'subtle'; // Mobile
    if (dimensions.width < 1024) return 'medium'; // Tablet
    return 'medium'; // Desktop
  }, [shouldDisableGlassEffects, shouldUseMinimalEffects, dimensions.width]);

  return {
    isLowPerformance,
    shouldReduceEffects,
    shouldDisableGlassEffects,
    shouldUseMinimalEffects,
    getOptimalGlassPreset,
    dimensions,
    performanceMetrics: {
      isHighMemoryUsage: isHighUsage,
      isLowFPS,
    },
  };
};

/**
 * CSS containment utility hook
 */
export const useCSSContainment = (
  containmentType: 'layout' | 'style' | 'paint' | 'size' | 'layout style paint' = 'layout style paint'
) => {
  const containerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Apply CSS containment
    container.style.contain = containmentType;
    
    // Force GPU acceleration for better performance
    container.style.willChange = 'transform, opacity';
    container.style.transform = 'translateZ(0)';

    return () => {
      container.style.contain = '';
      container.style.willChange = '';
      container.style.transform = '';
    };
  }, [containmentType]);

  return containerRef;
};

/**
 * Optimized animation hook with automatic cleanup
 */
export const useOptimizedAnimation = (
  animationCallback: (progress: number) => void,
  duration: number = 1000,
  enabled: boolean = true
) => {
  const animationRef = useRef<number>();
  const startTimeRef = useRef<number>();
  const { requestOptimizedFrame, cancelOptimizedFrame } = usePerformanceOptimization();

  const startAnimation = useCallback(() => {
    if (!enabled) return;

    startTimeRef.current = performance.now();
    
    const animate = (currentTime: number) => {
      if (!startTimeRef.current) return;
      
      const elapsed = currentTime - startTimeRef.current;
      const progress = Math.min(elapsed / duration, 1);
      
      animationCallback(progress);
      
      if (progress < 1) {
        animationRef.current = requestOptimizedFrame(animate);
      }
    };
    
    animationRef.current = requestOptimizedFrame(animate);
  }, [animationCallback, duration, enabled, requestOptimizedFrame]);

  const stopAnimation = useCallback(() => {
    cancelOptimizedFrame();
    startTimeRef.current = undefined;
  }, [cancelOptimizedFrame]);

  useEffect(() => {
    return () => {
      stopAnimation();
    };
  }, [stopAnimation]);

  return { startAnimation, stopAnimation };
};
