import { useState, useEffect, useCallback, useMemo } from 'react';
import { glassPresets, browserSupport, responsiveGlass, accessibilitySettings, type GlassEffectConfig } from '../config/glass.config';

/**
 * Browser detection utility
 */
const detectBrowser = (): keyof typeof browserSupport => {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (userAgent.includes('chrome') && !userAgent.includes('edge')) {
    return 'chrome';
  } else if (userAgent.includes('firefox')) {
    return 'firefox';
  } else if (userAgent.includes('safari') && !userAgent.includes('chrome')) {
    return 'safari';
  } else if (userAgent.includes('edge')) {
    return 'edge';
  }
  
  return 'chrome'; // Default fallback
};

/**
 * Device type detection
 */
const detectDeviceType = (): keyof typeof responsiveGlass => {
  const width = window.innerWidth;
  
  if (width < 768) return 'mobile';
  if (width < 1024) return 'tablet';
  return 'desktop';
};

/**
 * Check if user prefers reduced motion
 */
const prefersReducedMotion = (): boolean => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

/**
 * Check if user prefers high contrast
 */
const prefersHighContrast = (): boolean => {
  return window.matchMedia('(prefers-contrast: high)').matches;
};

interface UseGlassEffectsOptions {
  preset?: keyof typeof glassPresets;
  disabled?: boolean;
  respectMotionPreference?: boolean;
  customConfig?: Partial<GlassEffectConfig>;
}

interface GlassEffectsReturn {
  glassConfig: GlassEffectConfig;
  isSupported: boolean;
  deviceType: keyof typeof responsiveGlass;
  browser: keyof typeof browserSupport;
  shouldUseReducedEffects: boolean;
  glassStyles: React.CSSProperties;
  hoverStyles: React.CSSProperties;
  focusStyles: React.CSSProperties;
}

/**
 * Hook for managing glass effects with browser detection and accessibility
 */
export const useGlassEffects = (options: UseGlassEffectsOptions = {}): GlassEffectsReturn => {
  const {
    preset = 'medium',
    disabled = false,
    respectMotionPreference = true,
    customConfig = {},
  } = options;

  const [deviceType, setDeviceType] = useState<keyof typeof responsiveGlass>(() => detectDeviceType());
  const [reducedMotion, setReducedMotion] = useState(() => prefersReducedMotion());
  const [highContrast, setHighContrast] = useState(() => prefersHighContrast());

  const browser = useMemo(() => detectBrowser(), []);
  const isSupported = useMemo(() => browserSupport[browser].fullSupport, [browser]);

  // Handle window resize for responsive effects
  useEffect(() => {
    const handleResize = () => {
      setDeviceType(detectDeviceType());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle motion preference changes
  useEffect(() => {
    if (!respectMotionPreference) return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const handleChange = () => setReducedMotion(mediaQuery.matches);

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [respectMotionPreference]);

  // Handle contrast preference changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    const handleChange = () => setHighContrast(mediaQuery.matches);

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const shouldUseReducedEffects = useMemo(() => {
    return disabled || (respectMotionPreference && reducedMotion);
  }, [disabled, respectMotionPreference, reducedMotion]);

  // Calculate responsive glass configuration
  const glassConfig = useMemo((): GlassEffectConfig => {
    if (shouldUseReducedEffects) {
      return glassPresets.minimal.config;
    }

    const baseConfig = glassPresets[preset].config;
    const responsiveMultipliers = responsiveGlass[deviceType];

    const adjustedConfig = {
      ...baseConfig,
      displacementScale: baseConfig.displacementScale * responsiveMultipliers.displacementScale,
      blurAmount: baseConfig.blurAmount * responsiveMultipliers.blurAmount,
      elasticity: baseConfig.elasticity * responsiveMultipliers.elasticity,
      ...customConfig,
    };

    // Apply accessibility adjustments
    if (highContrast) {
      adjustedConfig.opacity = Math.min(adjustedConfig.opacity + 0.1, 1);
      adjustedConfig.border = adjustedConfig.border.replace(/0\.\d+/, '0.4');
    }

    return adjustedConfig;
  }, [preset, deviceType, shouldUseReducedEffects, customConfig, highContrast]);

  // Generate CSS styles for glass effects
  const glassStyles = useMemo((): React.CSSProperties => {
    const config = glassConfig;

    const baseStyles: React.CSSProperties = {
      background: config.background,
      backdropFilter: isSupported ? config.backdropBlur : 'none',
      WebkitBackdropFilter: isSupported ? config.backdropBlur : 'none',
      border: config.border,
      borderRadius: `${config.cornerRadius}px`,
      boxShadow: config.boxShadow,
      opacity: config.opacity,
      position: 'relative',
      overflow: 'hidden',
    };

    // Add performance optimizations
    if (isSupported) {
      baseStyles.willChange = 'transform, opacity';
      baseStyles.contain = 'layout style paint';
      baseStyles.transform = 'translateZ(0)'; // Force GPU acceleration
    }

    return baseStyles;
  }, [glassConfig, isSupported]);

  // Hover styles
  const hoverStyles = useMemo((): React.CSSProperties => {
    if (shouldUseReducedEffects) {
      return {
        transform: 'translateY(-1px)',
        boxShadow: '0 4px 16px 0 rgba(31, 38, 135, 0.2)',
      };
    }

    return {
      transform: 'translateY(-2px) scale(1.01)',
      boxShadow: '0 12px 40px 0 rgba(31, 38, 135, 0.3)',
      background: glassConfig.background.replace(/0\.\d+/, '0.2'),
    };
  }, [shouldUseReducedEffects, glassConfig]);

  // Focus styles
  const focusStyles = useMemo((): React.CSSProperties => ({
    outline: 'none',
    boxShadow: `${glassConfig.boxShadow}, 0 0 0 2px rgba(59, 130, 246, 0.5)`,
  }), [glassConfig]);

  return {
    glassConfig,
    isSupported,
    deviceType,
    browser,
    shouldUseReducedEffects,
    glassStyles,
    hoverStyles,
    focusStyles,
  };
};

/**
 * Hook for glass effect animations
 */
export const useGlassAnimations = (enabled: boolean = true) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isActive, setIsActive] = useState(false);

  const handleMouseEnter = useCallback(() => {
    if (enabled) setIsHovered(true);
  }, [enabled]);

  const handleMouseLeave = useCallback(() => {
    if (enabled) setIsHovered(false);
  }, [enabled]);

  const handleFocus = useCallback(() => {
    if (enabled) setIsFocused(true);
  }, [enabled]);

  const handleBlur = useCallback(() => {
    if (enabled) setIsFocused(false);
  }, [enabled]);

  const handleMouseDown = useCallback(() => {
    if (enabled) setIsActive(true);
  }, [enabled]);

  const handleMouseUp = useCallback(() => {
    if (enabled) setIsActive(false);
  }, [enabled]);

  return {
    isHovered,
    isFocused,
    isActive,
    handlers: {
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave,
      onFocus: handleFocus,
      onBlur: handleBlur,
      onMouseDown: handleMouseDown,
      onMouseUp: handleMouseUp,
    },
  };
};
