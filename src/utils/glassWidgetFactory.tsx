import React from 'react';
import { SisenseWidget } from '../components/widgets/SisenseWidget/SisenseWidget';
import { PlaceholderWidget } from '../components/widgets/PlaceholderWidget/PlaceholderWidget';
import { GlassWidgetContainer } from '../components/glass/GlassWidgetContainer';
import { ErrorBoundary } from '../components/ErrorBoundary/ErrorBoundary';
import { glassPresets, widgetGlassConfig } from '../config/glass.config';
import type { WidgetConfig } from '../types/dashboard.types';

// Check if we're in development mode without Sisense connection
const isDevelopmentMode = import.meta.env.DEV && !import.meta.env.VITE_INSTANCE_URL;

interface GlassWidgetOptions {
  enableGlassEffects?: boolean;
  glassPreset?: keyof typeof glassPresets;
  widgetType?: keyof typeof widgetGlassConfig;
  customGlassConfig?: any;
}

/**
 * Creates a widget component with optional glass effects using the official Sisense WidgetById component
 * 
 * This factory function creates widgets that use the official Sisense SDK WidgetById component
 * wrapped in glass containers for enhanced visual effects while maintaining full functionality.
 * 
 * @param widget - Widget configuration containing ID, position, and other properties
 * @param dashboardId - The Sisense dashboard ID that contains the widget
 * @param activeTab - The currently active tab to determine widget visibility
 * @param glassOptions - Options for glass effects configuration
 * @returns React element with either a glass-enhanced Sisense widget or placeholder widget, or null if not visible
 */
export const createGlassWidget = (
  widget: WidgetConfig, 
  dashboardId: string, 
  activeTab?: string,
  glassOptions: GlassWidgetOptions = {}
): React.ReactElement | null => {
  const {
    enableGlassEffects = true,
    glassPreset,
    widgetType = 'chart',
    customGlassConfig,
  } = glassOptions;

  // Check if widget should be visible based on active tab
  if (widget.visibleOn && activeTab && !widget.visibleOn.includes(activeTab)) {
    return null;
  }

  // Determine the appropriate widget type for glass configuration
  const effectiveWidgetType = widget.type === 'kpi' ? 'kpi' : 
                             widget.type === 'table' ? 'table' : 
                             widget.type === 'chart' ? 'chart' : 
                             widgetType;

  // Use placeholder widgets in development mode without Sisense connection
  if (isDevelopmentMode) {
    const placeholderWidget = (
      <PlaceholderWidget
        key={widget.id}
        title={widget.title}
        position={widget.position}
        type={widget.type as 'chart' | 'kpi' | 'table' | 'filter' | 'header' | 'info' | 'text'}
        widgetId={widget.id}
        config={widget.config}
      />
    );

    // Wrap placeholder in glass container if effects are enabled
    if (enableGlassEffects) {
      return (
        <GlassWidgetContainer
          key={widget.id}
          title={widget.title}
          position={widget.position}
          widgetType={effectiveWidgetType}
          glassPreset={glassPreset}
          enableGlassEffects={true}
          data-testid={`glass-widget-${widget.id}`}
        >
          {placeholderWidget}
        </GlassWidgetContainer>
      );
    }

    return placeholderWidget;
  }

  // Create the Sisense widget
  const sisenseWidget = (
    <SisenseWidget
      widgetId={widget.id}
      dashboardId={dashboardId}
      title={widget.title}
      position={widget.position}
      includeDashboardFilters={true}
      styleOptions={{
        height: widget.position.h * 120, // Dynamic height based on grid position
        // Width is handled by CSS layout, so we don't need to specify it here
        // The WidgetById component will use 100% width by default within its container
      }}
    />
  );

  // Wrap in glass container if effects are enabled
  if (enableGlassEffects) {
    return (
      <ErrorBoundary key={widget.id}>
        <GlassWidgetContainer
          title={widget.title}
          position={widget.position}
          widgetType={effectiveWidgetType}
          glassPreset={glassPreset}
          enableGlassEffects={true}
          data-testid={`glass-widget-${widget.id}`}
        >
          {sisenseWidget}
        </GlassWidgetContainer>
      </ErrorBoundary>
    );
  }

  // Fallback to original widget factory behavior
  return (
    <ErrorBoundary key={widget.id}>
      <GlassWidgetContainer
        title={widget.title}
        position={widget.position}
        widgetType={effectiveWidgetType}
        enableGlassEffects={false}
        data-testid={`widget-${widget.id}`}
      >
        {sisenseWidget}
      </GlassWidgetContainer>
    </ErrorBoundary>
  );
};

/**
 * Enhanced widget factory that automatically determines glass settings based on widget type
 */
export const createAutoGlassWidget = (
  widget: WidgetConfig, 
  dashboardId: string, 
  activeTab?: string,
  globalGlassEnabled: boolean = true
): React.ReactElement | null => {
  // Auto-determine glass settings based on widget type
  const widgetType = widget.type === 'kpi' ? 'kpi' : 
                    widget.type === 'table' ? 'table' : 
                    widget.type === 'chart' ? 'chart' : 
                    'chart';

  const glassPreset = widgetGlassConfig[widgetType];

  return createGlassWidget(widget, dashboardId, activeTab, {
    enableGlassEffects: globalGlassEnabled,
    glassPreset,
    widgetType,
  });
};

/**
 * Batch create widgets with consistent glass settings
 */
export const createGlassWidgetBatch = (
  widgets: WidgetConfig[],
  dashboardId: string,
  activeTab?: string,
  glassOptions: GlassWidgetOptions = {}
): (React.ReactElement | null)[] => {
  return widgets.map(widget => 
    createGlassWidget(widget, dashboardId, activeTab, glassOptions)
  ).filter(Boolean);
};

/**
 * Create widgets with staggered animation delays
 */
export const createAnimatedGlassWidgets = (
  widgets: WidgetConfig[],
  dashboardId: string,
  activeTab?: string,
  glassOptions: GlassWidgetOptions = {},
  animationDelay: number = 0.1
): React.ReactElement[] => {
  return widgets
    .map((widget, index) => {
      const widgetElement = createGlassWidget(widget, dashboardId, activeTab, glassOptions);
      
      if (!widgetElement) return null;

      // Wrap with animation container
      return (
        <div
          key={`animated-${widget.id}-${activeTab || 'default'}`}
          style={{
            animation: 'fadeInUp 0.6s ease-out',
            animationDelay: `${index * animationDelay}s`,
            animationFillMode: 'both',
          }}
        >
          {widgetElement}
        </div>
      );
    })
    .filter(Boolean) as React.ReactElement[];
};

/**
 * Widget factory with responsive glass effects
 */
export const createResponsiveGlassWidget = (
  widget: WidgetConfig,
  dashboardId: string,
  activeTab?: string,
  deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop'
): React.ReactElement | null => {
  // Adjust glass effects based on device type
  const glassOptions: GlassWidgetOptions = {
    enableGlassEffects: deviceType !== 'mobile', // Disable on mobile for performance
    glassPreset: deviceType === 'mobile' ? 'minimal' : 
                deviceType === 'tablet' ? 'subtle' : 
                'medium',
    widgetType: widget.type === 'kpi' ? 'kpi' : 
               widget.type === 'table' ? 'table' : 
               'chart',
  };

  return createGlassWidget(widget, dashboardId, activeTab, glassOptions);
};
