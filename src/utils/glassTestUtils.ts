/**
 * Glass Effects Testing and Validation Utilities
 * 
 * Provides utilities for testing glass effects functionality,
 * performance, and accessibility compliance.
 */

import { glassUtils } from './glassUtils';
import { glassPresets } from '../config/glass.config';

/**
 * Test suite for glass effects functionality
 */
export const glassTestSuite = {
  /**
   * Test browser compatibility
   */
  testBrowserCompatibility(): {
    browser: string;
    supportsBackdropFilter: boolean;
    supportsCSSFilters: boolean;
    supportsCSSTransforms: boolean;
    fullSupport: boolean;
    recommendations: string[];
  } {
    const capabilities = glassUtils.browser.getBrowserCapabilities();
    const recommendations: string[] = [];

    if (!capabilities.supportsBackdropFilter) {
      recommendations.push('Use CSS fallbacks for backdrop-filter');
    }

    if (!capabilities.supportsCSSFilters) {
      recommendations.push('Disable blur effects');
    }

    if (!capabilities.fullGlassSupport) {
      recommendations.push('Use minimal glass preset');
    }

    return {
      ...capabilities,
      recommendations,
    };
  },

  /**
   * Test performance metrics
   */
  async testPerformance(): Promise<{
    renderTime: number;
    memoryUsage?: number;
    fps: number;
    isPerformant: boolean;
    recommendations: string[];
  }> {
    const startTime = performance.now();
    const recommendations: string[] = [];

    // Simulate glass effect rendering
    const testElement = document.createElement('div');
    testElement.style.backdropFilter = 'blur(10px)';
    testElement.style.background = 'rgba(255, 255, 255, 0.1)';
    testElement.style.transform = 'translateZ(0)';
    document.body.appendChild(testElement);

    // Measure render time
    await new Promise(resolve => requestAnimationFrame(resolve));
    const renderTime = performance.now() - startTime;

    // Clean up
    document.body.removeChild(testElement);

    // Check memory usage if available
    let memoryUsage: number | undefined;
    if ('memory' in performance) {
      memoryUsage = (performance as any).memory.usedJSHeapSize;
    }

    // Estimate FPS (simplified)
    const fps = 1000 / renderTime;

    const isPerformant = renderTime < 16 && fps > 30; // 60fps target

    if (renderTime > 16) {
      recommendations.push('Consider reducing glass effect complexity');
    }

    if (fps < 30) {
      recommendations.push('Enable performance optimizations');
    }

    return {
      renderTime,
      memoryUsage,
      fps,
      isPerformant,
      recommendations,
    };
  },

  /**
   * Test accessibility compliance
   */
  testAccessibility(): {
    prefersReducedMotion: boolean;
    prefersHighContrast: boolean;
    colorBlindFriendly: boolean;
    keyboardAccessible: boolean;
    screenReaderFriendly: boolean;
    recommendations: string[];
  } {
    const preferences = glassUtils.accessibility.getAccessibilityPreferences();
    const recommendations: string[] = [];

    if (preferences.reducedMotion) {
      recommendations.push('Disable animations and transitions');
    }

    if (preferences.highContrast) {
      recommendations.push('Increase border opacity and contrast');
    }

    // Test color blind friendliness (simplified check)
    const colorBlindFriendly = true; // Glass effects are generally color-blind friendly

    // Test keyboard accessibility
    const keyboardAccessible = true; // Glass containers support focus states

    // Test screen reader friendliness
    const screenReaderFriendly = true; // Glass effects don't interfere with content

    return {
      ...preferences,
      colorBlindFriendly,
      keyboardAccessible,
      screenReaderFriendly,
      recommendations,
    };
  },

  /**
   * Test responsive behavior
   */
  testResponsiveness(): {
    deviceType: string;
    viewportWidth: number;
    viewportHeight: number;
    recommendedPreset: string;
    isTouch: boolean;
    recommendations: string[];
  } {
    const deviceType = glassUtils.device.detectDeviceType();
    const isTouch = glassUtils.device.isTouchDevice();
    const recommendations: string[] = [];

    let recommendedPreset = 'medium';

    if (deviceType === 'mobile') {
      recommendedPreset = 'minimal';
      recommendations.push('Use minimal effects on mobile devices');
    } else if (deviceType === 'tablet') {
      recommendedPreset = 'subtle';
      recommendations.push('Use subtle effects on tablets');
    }

    if (isTouch) {
      recommendations.push('Optimize hover effects for touch devices');
    }

    return {
      deviceType,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight,
      recommendedPreset,
      isTouch,
      recommendations,
    };
  },

  /**
   * Test glass preset configurations
   */
  testGlassPresets(): {
    presets: Array<{
      name: string;
      isValid: boolean;
      issues: string[];
    }>;
    recommendations: string[];
  } {
    const presetTests = Object.entries(glassPresets).map(([name, preset]) => {
      const issues: string[] = [];

      // Validate configuration values
      if (preset.config.opacity < 0 || preset.config.opacity > 1) {
        issues.push('Opacity should be between 0 and 1');
      }

      if (preset.config.displacementScale < 0) {
        issues.push('Displacement scale should be positive');
      }

      if (preset.config.blurAmount < 0) {
        issues.push('Blur amount should be positive');
      }

      if (preset.config.cornerRadius < 0) {
        issues.push('Corner radius should be positive');
      }

      return {
        name,
        isValid: issues.length === 0,
        issues,
      };
    });

    const recommendations: string[] = [];
    const invalidPresets = presetTests.filter(test => !test.isValid);

    if (invalidPresets.length > 0) {
      recommendations.push('Fix invalid preset configurations');
    }

    return {
      presets: presetTests,
      recommendations,
    };
  },

  /**
   * Run comprehensive test suite
   */
  async runFullTestSuite(): Promise<{
    browser: ReturnType<typeof glassTestSuite.testBrowserCompatibility>;
    performance: Awaited<ReturnType<typeof glassTestSuite.testPerformance>>;
    accessibility: ReturnType<typeof glassTestSuite.testAccessibility>;
    responsiveness: ReturnType<typeof glassTestSuite.testResponsiveness>;
    presets: ReturnType<typeof glassTestSuite.testGlassPresets>;
    overallScore: number;
    overallRecommendations: string[];
  }> {
    const browser = this.testBrowserCompatibility();
    const performance = await this.testPerformance();
    const accessibility = this.testAccessibility();
    const responsiveness = this.testResponsiveness();
    const presets = this.testGlassPresets();

    // Calculate overall score (0-100)
    let score = 100;

    // Deduct points for issues
    if (!browser.fullSupport) score -= 20;
    if (!performance.isPerformant) score -= 25;
    if (accessibility.prefersReducedMotion) score -= 10;
    if (responsiveness.deviceType === 'mobile') score -= 10;
    if (presets.presets.some(p => !p.isValid)) score -= 15;

    // Collect all recommendations
    const overallRecommendations = [
      ...browser.recommendations,
      ...performance.recommendations,
      ...accessibility.recommendations,
      ...responsiveness.recommendations,
      ...presets.recommendations,
    ];

    return {
      browser,
      performance,
      accessibility,
      responsiveness,
      presets,
      overallScore: Math.max(0, score),
      overallRecommendations,
    };
  },
};

/**
 * Visual regression testing utilities
 */
export const visualTestUtils = {
  /**
   * Capture element screenshot for comparison
   */
  async captureElementScreenshot(element: HTMLElement): Promise<string | null> {
    if (!('html2canvas' in window)) {
      console.warn('html2canvas not available for visual testing');
      return null;
    }

    try {
      // This would require html2canvas library
      // const canvas = await html2canvas(element);
      // return canvas.toDataURL();
      return 'screenshot-placeholder';
    } catch (error) {
      console.error('Failed to capture screenshot:', error);
      return null;
    }
  },

  /**
   * Compare two screenshots for differences
   */
  compareScreenshots(screenshot1: string, screenshot2: string): {
    isDifferent: boolean;
    differencePercentage: number;
  } {
    // Simplified comparison - in real implementation would use image diff library
    const isDifferent = screenshot1 !== screenshot2;
    const differencePercentage = isDifferent ? 50 : 0; // Placeholder

    return {
      isDifferent,
      differencePercentage,
    };
  },
};

/**
 * Performance monitoring utilities
 */
export const performanceMonitor = {
  /**
   * Start performance monitoring
   */
  startMonitoring(): {
    stop: () => PerformanceReport;
  } {
    const startTime = performance.now();
    let frameCount = 0;
    let animationId: number;

    const measureFrame = () => {
      frameCount++;
      animationId = requestAnimationFrame(measureFrame);
    };

    animationId = requestAnimationFrame(measureFrame);

    return {
      stop: () => {
        cancelAnimationFrame(animationId);
        const endTime = performance.now();
        const duration = endTime - startTime;
        const fps = Math.round((frameCount * 1000) / duration);

        return {
          duration,
          frameCount,
          fps,
          averageFrameTime: duration / frameCount,
          isPerformant: fps >= 30,
        };
      },
    };
  },
};

interface PerformanceReport {
  duration: number;
  frameCount: number;
  fps: number;
  averageFrameTime: number;
  isPerformant: boolean;
}

/**
 * Automated testing utilities
 */
export const automatedTests = {
  /**
   * Test glass container rendering
   */
  testGlassContainerRendering(container: HTMLElement): {
    hasGlassStyles: boolean;
    hasBackdropFilter: boolean;
    hasProperOpacity: boolean;
    hasTransform: boolean;
    issues: string[];
  } {
    const styles = window.getComputedStyle(container);
    const issues: string[] = [];

    const hasBackdropFilter = styles.backdropFilter !== 'none';
    const hasProperOpacity = parseFloat(styles.opacity) > 0 && parseFloat(styles.opacity) <= 1;
    const hasTransform = styles.transform !== 'none';
    const hasGlassStyles = hasBackdropFilter || styles.background.includes('rgba');

    if (!hasGlassStyles) {
      issues.push('No glass styles detected');
    }

    if (!hasProperOpacity) {
      issues.push('Invalid opacity value');
    }

    return {
      hasGlassStyles,
      hasBackdropFilter,
      hasProperOpacity,
      hasTransform,
      issues,
    };
  },

  /**
   * Test widget functionality within glass containers
   */
  testWidgetFunctionality(widgetContainer: HTMLElement): {
    isInteractive: boolean;
    hasContent: boolean;
    isAccessible: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    const isInteractive = widgetContainer.tabIndex >= 0 || 
                         widgetContainer.getAttribute('role') === 'button';
    
    const hasContent = widgetContainer.children.length > 0;
    
    const isAccessible = widgetContainer.getAttribute('aria-label') !== null ||
                        widgetContainer.getAttribute('aria-labelledby') !== null;

    if (!hasContent) {
      issues.push('Widget container has no content');
    }

    if (isInteractive && !isAccessible) {
      issues.push('Interactive widget lacks accessibility attributes');
    }

    return {
      isInteractive,
      hasContent,
      isAccessible,
      issues,
    };
  },
};
