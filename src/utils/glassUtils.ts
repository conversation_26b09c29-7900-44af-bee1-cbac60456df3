/**
 * Glass Effects Utility Functions
 * 
 * Provides utility functions for browser detection, feature support,
 * and glass effect calculations.
 */

import { browserSupport, responsiveGlass, accessibilitySettings } from '../config/glass.config';

/**
 * Browser detection utilities
 */
export const browserUtils = {
  /**
   * Detect the current browser
   */
  detectBrowser(): keyof typeof browserSupport {
    if (typeof window === 'undefined') return 'chrome';
    
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (userAgent.includes('chrome') && !userAgent.includes('edge')) {
      return 'chrome';
    } else if (userAgent.includes('firefox')) {
      return 'firefox';
    } else if (userAgent.includes('safari') && !userAgent.includes('chrome')) {
      return 'safari';
    } else if (userAgent.includes('edge')) {
      return 'edge';
    }
    
    return 'chrome'; // Default fallback
  },

  /**
   * Check if the browser supports backdrop-filter
   */
  supportsBackdropFilter(): boolean {
    if (typeof window === 'undefined') return false;
    
    const testElement = document.createElement('div');
    testElement.style.backdropFilter = 'blur(1px)';
    return testElement.style.backdropFilter !== '';
  },

  /**
   * Check if the browser supports CSS filters
   */
  supportsCSSFilters(): boolean {
    if (typeof window === 'undefined') return false;
    
    const testElement = document.createElement('div');
    testElement.style.filter = 'blur(1px)';
    return testElement.style.filter !== '';
  },

  /**
   * Check if the browser supports CSS transforms
   */
  supportsCSSTransforms(): boolean {
    if (typeof window === 'undefined') return false;
    
    const testElement = document.createElement('div');
    testElement.style.transform = 'translateZ(0)';
    return testElement.style.transform !== '';
  },

  /**
   * Get comprehensive browser capabilities
   */
  getBrowserCapabilities() {
    const browser = this.detectBrowser();
    const support = browserSupport[browser];
    
    return {
      browser,
      supportsBackdropFilter: this.supportsBackdropFilter(),
      supportsCSSFilters: this.supportsCSSFilters(),
      supportsCSSTransforms: this.supportsCSSTransforms(),
      supportsDisplacement: support.displacement,
      fullGlassSupport: support.fullSupport,
      recommendedFallback: !support.fullSupport,
    };
  },
};

/**
 * Device and viewport utilities
 */
export const deviceUtils = {
  /**
   * Detect device type based on viewport width
   */
  detectDeviceType(): keyof typeof responsiveGlass {
    if (typeof window === 'undefined') return 'desktop';
    
    const width = window.innerWidth;
    
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  },

  /**
   * Check if device is touch-enabled
   */
  isTouchDevice(): boolean {
    if (typeof window === 'undefined') return false;
    
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  },

  /**
   * Get device pixel ratio
   */
  getPixelRatio(): number {
    if (typeof window === 'undefined') return 1;
    
    return window.devicePixelRatio || 1;
  },

  /**
   * Check if device has reduced performance capabilities
   */
  isLowPerformanceDevice(): boolean {
    if (typeof window === 'undefined') return false;
    
    // Check for indicators of low-performance devices
    const hardwareConcurrency = (navigator as any).hardwareConcurrency || 1;
    const deviceMemory = (navigator as any).deviceMemory || 1;
    const pixelRatio = this.getPixelRatio();
    
    return hardwareConcurrency <= 2 || deviceMemory <= 2 || pixelRatio < 1.5;
  },
};

/**
 * Accessibility utilities
 */
export const accessibilityUtils = {
  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion(): boolean {
    if (typeof window === 'undefined') return false;
    
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  /**
   * Check if user prefers high contrast
   */
  prefersHighContrast(): boolean {
    if (typeof window === 'undefined') return false;
    
    return window.matchMedia('(prefers-contrast: high)').matches;
  },

  /**
   * Check if user prefers dark color scheme
   */
  prefersDarkMode(): boolean {
    if (typeof window === 'undefined') return false;
    
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  },

  /**
   * Get accessibility preferences
   */
  getAccessibilityPreferences() {
    return {
      reducedMotion: this.prefersReducedMotion(),
      highContrast: this.prefersHighContrast(),
      darkMode: this.prefersDarkMode(),
    };
  },

  /**
   * Apply accessibility adjustments to glass config
   */
  applyAccessibilityAdjustments(config: any, preferences?: ReturnType<typeof this.getAccessibilityPreferences>) {
    const prefs = preferences || this.getAccessibilityPreferences();
    const adjustedConfig = { ...config };

    if (prefs.reducedMotion) {
      adjustedConfig.elasticity = Math.min(adjustedConfig.elasticity * 0.3, 0.05);
      adjustedConfig.displacementScale = Math.min(adjustedConfig.displacementScale * 0.5, 20);
    }

    if (prefs.highContrast) {
      adjustedConfig.opacity = Math.min(adjustedConfig.opacity + 0.1, 1);
      adjustedConfig.border = adjustedConfig.border.replace(/0\.\d+/, '0.4');
    }

    return adjustedConfig;
  },
};

/**
 * Performance utilities
 */
export const performanceUtils = {
  /**
   * Check if GPU acceleration is available
   */
  supportsGPUAcceleration(): boolean {
    if (typeof window === 'undefined') return false;
    
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    return !!gl;
  },

  /**
   * Debounce function for performance optimization
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate?: boolean
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null;
    
    return function executedFunction(...args: Parameters<T>) {
      const later = () => {
        timeout = null;
        if (!immediate) func(...args);
      };
      
      const callNow = immediate && !timeout;
      
      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      
      if (callNow) func(...args);
    };
  },

  /**
   * Throttle function for performance optimization
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return function executedFunction(...args: Parameters<T>) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * Request animation frame with fallback
   */
  requestAnimationFrame(callback: FrameRequestCallback): number {
    if (typeof window === 'undefined') return 0;
    
    return window.requestAnimationFrame || 
           window.webkitRequestAnimationFrame || 
           ((cb) => window.setTimeout(cb, 1000 / 60));
  },
};

/**
 * Glass effect calculation utilities
 */
export const glassCalculations = {
  /**
   * Calculate responsive multipliers based on device type
   */
  getResponsiveMultipliers(deviceType: keyof typeof responsiveGlass) {
    return responsiveGlass[deviceType];
  },

  /**
   * Calculate mouse-based displacement
   */
  calculateDisplacement(
    mouseX: number,
    mouseY: number,
    elementRect: DOMRect,
    elasticity: number,
    maxDisplacement: number
  ) {
    const centerX = elementRect.width / 2;
    const centerY = elementRect.height / 2;
    
    const deltaX = mouseX - centerX;
    const deltaY = mouseY - centerY;
    
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const maxDistance = Math.sqrt(centerX * centerX + centerY * centerY);
    
    const normalizedDistance = Math.min(distance / maxDistance, 1);
    const displacement = normalizedDistance * elasticity * maxDisplacement;
    
    return {
      x: (deltaX / distance) * displacement || 0,
      y: (deltaY / distance) * displacement || 0,
      rotation: (deltaX / maxDistance) * elasticity * 2,
    };
  },

  /**
   * Interpolate between two values
   */
  lerp(start: number, end: number, factor: number): number {
    return start + (end - start) * factor;
  },

  /**
   * Ease-out cubic function
   */
  easeOutCubic(t: number): number {
    return 1 - Math.pow(1 - t, 3);
  },

  /**
   * Ease-in-out cubic function
   */
  easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  },
};

/**
 * Main utility object combining all utilities
 */
export const glassUtils = {
  browser: browserUtils,
  device: deviceUtils,
  accessibility: accessibilityUtils,
  performance: performanceUtils,
  calculations: glassCalculations,
  
  /**
   * Get comprehensive environment info for glass effects
   */
  getEnvironmentInfo() {
    return {
      browser: browserUtils.getBrowserCapabilities(),
      device: {
        type: deviceUtils.detectDeviceType(),
        isTouch: deviceUtils.isTouchDevice(),
        pixelRatio: deviceUtils.getPixelRatio(),
        isLowPerformance: deviceUtils.isLowPerformanceDevice(),
      },
      accessibility: accessibilityUtils.getAccessibilityPreferences(),
      performance: {
        supportsGPU: performanceUtils.supportsGPUAcceleration(),
      },
    };
  },
  
  /**
   * Determine if glass effects should be enabled
   */
  shouldEnableGlassEffects(options: {
    respectPerformance?: boolean;
    respectAccessibility?: boolean;
    respectBrowserSupport?: boolean;
  } = {}) {
    const {
      respectPerformance = true,
      respectAccessibility = true,
      respectBrowserSupport = true,
    } = options;
    
    const env = this.getEnvironmentInfo();
    
    if (respectBrowserSupport && !env.browser.fullGlassSupport) {
      return false;
    }
    
    if (respectAccessibility && env.accessibility.reducedMotion) {
      return false;
    }
    
    if (respectPerformance && env.device.isLowPerformance) {
      return false;
    }
    
    return true;
  },
};
