import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { SisenseContextProvider } from '@sisense/sdk-ui';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <SisenseContextProvider
      url={import.meta.env.VITE_INSTANCE_URL}
      ssoEnabled={true}
    >
      <App />
    </SisenseContextProvider>
  </StrictMode>,
)
