import React from 'react';
import { theme } from '../../config/theme.config';
import { GlassContainer, GlassBackground } from '../glass';

interface GlassPageLayoutProps {
  children: React.ReactNode;
  title?: string;
  showHeader?: boolean;
  enableGlassEffects?: boolean;
  backgroundVariant?: 'subtle' | 'gradient' | 'mesh' | 'dots' | 'waves';
  headerGlassPreset?: 'subtle' | 'medium' | 'prominent' | 'minimal';
}

/**
 * GlassPageLayout - Enhanced page layout with glass effects
 * 
 * Provides the same functionality as PageLayout but with optional
 * glass effects for modern glassmorphism styling.
 */
export const GlassPageLayout: React.FC<GlassPageLayoutProps> = ({
  children,
  title,
  showHeader = true,
  enableGlassEffects = true,
  backgroundVariant = 'gradient',
  headerGlassPreset = 'medium',
}) => {
  const containerStyles: React.CSSProperties = {
    minHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
  };

  const headerStyles: React.CSSProperties = {
    padding: `${theme.spacing.lg}px ${theme.spacing.xl}px`,
    position: 'sticky',
    top: 0,
    zIndex: 1000,
    borderBottom: `1px solid ${theme.colors.border.light}`,
  };

  const fallbackHeaderStyles: React.CSSProperties = {
    background: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(10px)',
    boxShadow: theme.shadows.sm,
  };

  const headerContentStyles: React.CSSProperties = {
    maxWidth: '1400px',
    margin: '0 auto',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  };

  const titleStyles: React.CSSProperties = {
    margin: 0,
    color: theme.colors.text.primary,
    fontSize: theme.typography.fontSize.xxl,
    fontWeight: theme.typography.fontWeight.semibold,
    fontFamily: theme.typography.fontFamily.sans,
  };

  const avatarStyles: React.CSSProperties = {
    width: 32,
    height: 32,
    borderRadius: '50%',
    background: theme.colors.gradients.primary,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.semibold,
  };

  const mainStyles: React.CSSProperties = {
    flex: 1,
    padding: `${theme.spacing.xl}px ${theme.spacing.lg}px`,
    maxWidth: '1400px',
    margin: '0 auto',
    width: '100%',
    position: 'relative',
    zIndex: 1,
  };

  return (
    <div style={containerStyles}>
      {/* Enhanced background */}
      {enableGlassEffects && (
        <GlassBackground 
          variant={backgroundVariant}
          opacity={0.4}
          animated={true}
        />
      )}
      
      {/* Background fallback for non-glass mode */}
      {!enableGlassEffects && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: theme.colors.backgroundGradient,
          zIndex: -1,
        }} />
      )}

      {showHeader && (
        <header style={headerStyles}>
          {enableGlassEffects ? (
            <GlassContainer
              preset={headerGlassPreset}
              style={{
                padding: theme.spacing.md,
                width: '100%',
              }}
              enableHoverEffects={false}
              enableClickEffects={false}
            >
              <div style={headerContentStyles}>
                <h1 style={titleStyles}>
                  {title || 'ARTBA Dashboard'}
                </h1>

                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: theme.spacing.md,
                }}>
                  <div style={avatarStyles}>
                    A
                  </div>
                </div>
              </div>
            </GlassContainer>
          ) : (
            <div style={{
              ...fallbackHeaderStyles,
              borderRadius: theme.borderRadius.lg,
              padding: theme.spacing.md,
            }}>
              <div style={headerContentStyles}>
                <h1 style={titleStyles}>
                  {title || 'ARTBA Dashboard'}
                </h1>

                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: theme.spacing.md,
                }}>
                  <div style={avatarStyles}>
                    A
                  </div>
                </div>
              </div>
            </div>
          )}
        </header>
      )}

      <main style={mainStyles}>
        {children}
      </main>
    </div>
  );
};
