import React from 'react';
import { theme } from '../../config/theme.config';

interface DashboardLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  actions?: React.ReactNode;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  title,
  description,
  actions
}) => {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: theme.spacing.xl,
      padding: `${theme.spacing.lg}px`,
      background: theme.colors.backgroundGradient,
      minHeight: '100vh',
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingBottom: theme.spacing.lg,
        borderBottom: `1px solid ${theme.colors.border}`,
      }}>
        <div>
          <h2 style={{
            margin: 0,
            color: theme.colors.text.primary,
            fontSize: '32px',
            fontWeight: 700,
            letterSpacing: '-0.5px',
          }}>
            {title}
          </h2>
          {description && (
            <p style={{
              margin: `${theme.spacing.sm}px 0 0 0`,
              color: theme.colors.text.secondary,
              fontSize: '16px',
              lineHeight: 1.5,
              maxWidth: '600px',
            }}>
              {description}
            </p>
          )}
        </div>
        {actions && (
          <div style={{
            display: 'flex',
            gap: theme.spacing.md,
            alignItems: 'center',
          }}>
            {actions}
          </div>
        )}
      </div>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(12, 1fr)',
        gap: theme.spacing.lg,
        gridAutoRows: 'minmax(120px, auto)',
        width: '100%',
        maxWidth: '1400px',
        margin: '0 auto',
        padding: `0 ${theme.spacing.lg}px`,
      }}>
        {children}
      </div>
    </div>
  );
};