import React from 'react';
import { theme } from '../../config/theme.config';
import { GlassContainer } from '../glass';

interface GlassDashboardHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
  enableGlassEffects?: boolean;
  glassPreset?: 'subtle' | 'medium' | 'prominent' | 'minimal';
}

/**
 * GlassDashboardHeader - Enhanced dashboard header with glass effects
 * 
 * Provides the same functionality as DashboardHeader but with optional
 * glass effects for modern glassmorphism styling.
 */
export const GlassDashboardHeader: React.FC<GlassDashboardHeaderProps> = ({
  title,
  description,
  actions,
  className = '',
  enableGlassEffects = true,
  glassPreset = 'medium',
}) => {
  const containerStyles: React.CSSProperties = {
    paddingBottom: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
    position: 'relative',
    overflow: 'hidden',
    flexDirection: 'row',
    gap: theme.spacing.lg,
    flexWrap: 'wrap',
  };

  const fallbackStyles: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    borderBottom: `2px solid ${theme.colors.border.light}`,
    background: theme.colors.gradients.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    boxShadow: theme.shadows.card,
  };

  const glassContentStyles: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    width: '100%',
    gap: theme.spacing.lg,
    flexWrap: 'wrap',
  };

  const decorationStyles: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    right: 0,
    width: '200px',
    height: '200px',
    background: theme.colors.gradients.primary,
    opacity: 0.05,
    borderRadius: '50%',
    transform: 'translate(50%, -50%)',
    pointerEvents: 'none',
  };

  const titleContainerStyles: React.CSSProperties = {
    flex: 1,
    zIndex: 1,
  };

  const titleStyles: React.CSSProperties = {
    margin: 0,
    color: theme.colors.text.primary,
    fontSize: '36px',
    fontWeight: 800,
    letterSpacing: '-0.8px',
    lineHeight: 1.2,
    background: theme.colors.gradients.primary,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    marginBottom: theme.spacing.sm,
  };

  const descriptionStyles: React.CSSProperties = {
    margin: 0,
    color: theme.colors.text.secondary,
    fontSize: '18px',
    lineHeight: 1.6,
    maxWidth: '700px',
    fontWeight: 400,
  };

  const actionsContainerStyles: React.CSSProperties = {
    display: 'flex',
    gap: theme.spacing.md,
    alignItems: 'center',
    flexShrink: 0,
    zIndex: 1,
  };

  const headerContent = (
    <>
      {/* Background decoration */}
      <div style={decorationStyles} />
      
      <div style={titleContainerStyles}>
        <h1 style={titleStyles}>
          {title}
        </h1>
        {description && (
          <p style={descriptionStyles}>
            {description}
          </p>
        )}
      </div>
      
      {actions && (
        <div style={actionsContainerStyles}>
          {actions}
        </div>
      )}
    </>
  );

  if (enableGlassEffects) {
    return (
      <div className={className} style={containerStyles}>
        <GlassContainer
          preset={glassPreset}
          style={{
            padding: theme.spacing.xl,
            width: '100%',
          }}
          enableHoverEffects={false}
          enableClickEffects={false}
          className="glass-dashboard-header"
        >
          <div style={glassContentStyles}>
            {headerContent}
          </div>
        </GlassContainer>
      </div>
    );
  }

  // Fallback without glass effects
  return (
    <div
      className={className}
      style={{
        ...containerStyles,
        ...fallbackStyles,
      }}
    >
      {headerContent}
    </div>
  );
};
