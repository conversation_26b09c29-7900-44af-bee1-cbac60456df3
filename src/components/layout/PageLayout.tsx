import React from 'react';
import { theme } from '../../config/theme.config';

interface PageLayoutProps {
  children: React.ReactNode;
  title?: string;
  showHeader?: boolean;
}

export const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  title,
  showHeader = true
}) => {
  return (
    <div style={{
      minHeight: '100vh',
      background: theme.colors.backgroundGradient,
      display: 'flex',
      flexDirection: 'column'
    }}>
      {showHeader && (
        <header style={{
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          boxShadow: theme.shadows.sm,
          padding: `${theme.spacing.lg}px ${theme.spacing.xl}px`,
          position: 'sticky',
          top: 0,
          zIndex: 1000,
          borderBottom: `1px solid ${theme.colors.border.light}`,
        }}>
          <div style={{
            maxWidth: '1400px',
            margin: '0 auto',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
            <h1 style={{
              margin: 0,
              color: theme.colors.text.primary,
              fontSize: theme.typography.fontSize['2xl'],
              fontWeight: theme.typography.fontWeight.semibold,
              fontFamily: theme.typography.fontFamily.sans,
            }}>
              {title || 'ARTBA Dashboard'}
            </h1>

            {/* Optional header actions */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: theme.spacing.md,
            }}>
              <div style={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                background: theme.colors.gradients.primary,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: theme.typography.fontSize.sm,
                fontWeight: theme.typography.fontWeight.semibold,
              }}>
                A
              </div>
            </div>
          </div>
        </header>
      )}
      <main style={{
        flex: 1,
        padding: `${theme.spacing.xl}px ${theme.spacing.lg}px`,
        maxWidth: '1400px',
        margin: '0 auto',
        width: '100%',
      }}>
        {children}
      </main>
    </div>
  );
};