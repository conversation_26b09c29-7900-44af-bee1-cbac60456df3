import React from 'react';
import { useNavigate } from 'react-router-dom';
import { theme } from '../../config/theme.config';

interface DashboardActionsProps {
  onExport?: () => void;
  backPath?: string;
  exportLabel?: string;
  showBackButton?: boolean;
  showExportButton?: boolean;
  additionalActions?: React.ReactNode;
}

export const DashboardActions: React.FC<DashboardActionsProps> = ({
  onExport,
  backPath = '/',
  exportLabel = 'Export Report',
  showBackButton = true,
  showExportButton = true,
  additionalActions
}) => {
  const navigate = useNavigate();

  const handleExport = () => {
    if (onExport) {
      onExport();
    } else {
      // Default export functionality
      console.log('Export functionality not implemented');
    }
  };

  const buttonBaseStyle = {
    borderRadius: theme.borderRadius.lg,
    padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
    fontSize: '14px',
    fontWeight: 600,
    cursor: 'pointer',
    transition: `all ${theme.transitions.fast}`,
    border: 'none',
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing.sm,
    minHeight: '44px',
    fontFamily: theme.typography.fontFamily.sans,
  };

  const primaryButtonStyle = {
    ...buttonBaseStyle,
    background: theme.colors.gradients.primary,
    color: 'white',
    boxShadow: theme.shadows.md,
  };

  const secondaryButtonStyle = {
    ...buttonBaseStyle,
    backgroundColor: 'transparent',
    color: theme.colors.primary,
    border: `2px solid ${theme.colors.primary}`,
    boxShadow: theme.shadows.sm,
  };

  return (
    <div style={{
      display: 'flex',
      gap: theme.spacing.md,
      alignItems: 'center',
      flexWrap: 'wrap',
    }}>
      {showBackButton && (
        <button
          onClick={() => navigate(backPath)}
          style={secondaryButtonStyle}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = theme.colors.primary;
            e.currentTarget.style.color = 'white';
            e.currentTarget.style.transform = 'translateY(-1px)';
            e.currentTarget.style.boxShadow = theme.shadows.lg;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.color = theme.colors.primary;
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = theme.shadows.sm;
          }}
        >
          <span style={{ fontSize: '16px' }}>←</span>
          Back to Home
        </button>
      )}
      
      {additionalActions}
      
      {showExportButton && (
        <button
          onClick={handleExport}
          style={primaryButtonStyle}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-1px)';
            e.currentTarget.style.boxShadow = theme.shadows.lg;
            e.currentTarget.style.background = theme.colors.gradients.accent;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = theme.shadows.md;
            e.currentTarget.style.background = theme.colors.gradients.primary;
          }}
        >
          <span style={{ fontSize: '16px' }}>📊</span>
          {exportLabel}
        </button>
      )}
    </div>
  );
};
