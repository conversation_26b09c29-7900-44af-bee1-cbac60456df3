import React from 'react';
import { WidgetById } from '@sisense/sdk-ui';
import { WidgetContainer } from '../../layout/WidgetContainer';
import type { WidgetPosition } from '../../../types/dashboard.types';

interface SisenseWidgetProps {
  widgetId: string;
  dashboardId: string;
  title?: string;
  position: WidgetPosition;
  includeDashboardFilters?: boolean;
  styleOptions?: {
    height?: number;
    width?: number;
    [key: string]: unknown;
  };
}

export const SisenseWidget: React.FC<SisenseWidgetProps> = ({
  widgetId,
  dashboardId,
  title,
  position,
  includeDashboardFilters = true,
  styleOptions,
}) => {
  // Calculate dynamic height based on widget position
  const defaultHeight = position.h * 120; // Approximate height per grid row

  const widgetStyleOptions = {
    height: defaultHeight,
    width: styleOptions?.width || undefined,
    ...styleOptions,
  };

  return (
    <WidgetContainer title={title} position={position}>
      <WidgetById
        widgetOid={widgetId}
        dashboardOid={dashboardId}
        includeDashboardFilters={includeDashboardFilters}
        styleOptions={widgetStyleOptions}
      />
    </WidgetContainer>
  );
};