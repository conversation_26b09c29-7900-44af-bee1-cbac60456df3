import React from 'react';
import { theme } from '../../../config/theme.config';
import type { WidgetPosition } from '../../../types/dashboard.types';

interface PlaceholderWidgetProps {
  title?: string;
  position: WidgetPosition;
  type: 'chart' | 'kpi' | 'table' | 'filter' | 'pivot' | 'header' | 'info' | 'text';
  widgetId: string;
  config?: any;
}

export const PlaceholderWidget: React.FC<PlaceholderWidgetProps> = ({
  title,
  position,
  type,
  widgetId,
  config,
}) => {
  const renderPlaceholder = () => {
    switch (type) {
      case 'kpi':
        return (
          <div style={{
            textAlign: 'center',
            padding: theme.spacing.xl,
          }}>
            <div style={{
              fontSize: '48px',
              fontWeight: 700,
              color: config?.color || theme.colors.primary,
              marginBottom: theme.spacing.sm,
            }}>
              {config?.prefix}{(config?.value / 1000000000).toFixed(1)}{config?.suffix}
            </div>
            {config?.subtitle && (
              <div style={{
                fontSize: theme.typography.fontSize.sm,
                color: theme.colors.text.secondary,
              }}>
                {config.subtitle}
              </div>
            )}
          </div>
        );

      case 'chart':
        return (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            padding: theme.spacing.md,
          }}>
            <div style={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <svg width="100%" height="100%" viewBox="0 0 400 200" style={{ maxHeight: '200px' }}>
              {config?.chartType === 'pie' ? (
                <g transform="translate(200, 100)">
                  <circle
                    cx="0"
                    cy="0"
                    r="80"
                    fill="none"
                    stroke={theme.colors.border.light}
                    strokeWidth="40"
                  />
                  <path
                    d="M 0,-80 A 80,80 0 1,1 -56.57,56.57 L 0,0 Z"
                    fill={theme.colors.primary}
                    opacity="0.2"
                  />
                </g>
              ) : (
                <g>
                  {[30, 60, 45, 80, 65, 90].map((height, i) => (
                    <rect
                      key={i}
                      x={50 + i * 50}
                      y={200 - height * 2}
                      width="40"
                      height={height * 2}
                      fill={theme.colors.primary}
                      opacity="0.2"
                      rx="4"
                    />
                  ))}
                </g>
              )}
              </svg>
            </div>
            <div style={{
              marginTop: theme.spacing.sm,
              fontSize: theme.typography.fontSize.sm,
              color: theme.colors.text.secondary,
              textAlign: 'center',
            }}>
              {config?.chartType || 'Chart'} Visualization
            </div>
          </div>
        );

      case 'table':
        return (
          <div style={{
            width: '100%',
            height: '100%',
            overflow: 'auto',
            display: 'flex',
            flexDirection: 'column',
          }}>
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
              fontSize: theme.typography.fontSize.sm,
            }}>
              <thead>
                <tr style={{
                  borderBottom: `2px solid ${theme.colors.border.light}`,
                }}>
                  {(config?.columns || ['Column 1', 'Column 2', 'Column 3']).map((col: string, index: number) => (
                    <th key={index} style={{
                      padding: theme.spacing.md,
                      textAlign: 'left',
                      fontWeight: theme.typography.fontWeight.medium,
                      color: theme.colors.text.secondary,
                    }}>
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {[1, 2, 3].map((row) => (
                  <tr key={row} style={{
                    borderBottom: `1px solid ${theme.colors.border.light}`,
                  }}>
                    {(config?.columns || ['', '', '']).map((_: string, index: number) => (
                      <td key={index} style={{
                        padding: theme.spacing.md,
                        color: theme.colors.text.primary,
                      }}>
                        <div style={{
                          width: `${60 + Math.random() * 40}%`,
                          height: '12px',
                          backgroundColor: theme.colors.border.light,
                          borderRadius: theme.borderRadius.sm,
                        }} />
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'pivot':
        return (
          <div style={{
            width: '100%',
            height: '100%',
            overflow: 'auto',
            display: 'flex',
            flexDirection: 'column',
          }}>
            <div style={{
              padding: theme.spacing.sm,
              backgroundColor: theme.colors.background.secondary,
              borderBottom: `1px solid ${theme.colors.border.light}`,
              fontSize: theme.typography.fontSize.xs,
              color: theme.colors.text.secondary,
              fontWeight: theme.typography.fontWeight.medium,
            }}>
              Pivot Table
            </div>
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
              fontSize: theme.typography.fontSize.sm,
            }}>
              <thead>
                <tr style={{
                  borderBottom: `2px solid ${theme.colors.border.light}`,
                  backgroundColor: theme.colors.background.light,
                }}>
                  {['Dimension', '2021', '2022', '2023', '2024', 'Total'].map((col: string, index: number) => (
                    <th key={index} style={{
                      padding: theme.spacing.md,
                      textAlign: index === 0 ? 'left' : 'right',
                      fontWeight: theme.typography.fontWeight.medium,
                      color: theme.colors.text.secondary,
                    }}>
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {[1, 2, 3, 4].map((row) => (
                  <tr key={row} style={{
                    borderBottom: `1px solid ${theme.colors.border.light}`,
                  }}>
                    <td style={{
                      padding: theme.spacing.md,
                      color: theme.colors.text.primary,
                      fontWeight: theme.typography.fontWeight.medium,
                    }}>
                      Category {row}
                    </td>
                    {[1, 2, 3, 4, 5].map((col) => (
                      <td key={col} style={{
                        padding: theme.spacing.md,
                        color: theme.colors.text.primary,
                        textAlign: 'right',
                      }}>
                        <div style={{
                          width: `${50 + Math.random() * 30}%`,
                          height: '12px',
                          backgroundColor: theme.colors.border.light,
                          borderRadius: theme.borderRadius.sm,
                          marginLeft: 'auto',
                        }} />
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'header':
        return (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing.lg,
            padding: theme.spacing.lg,
          }}>
            <div style={{
              width: '80px',
              height: '80px',
              borderRadius: '50%',
              backgroundColor: theme.colors.border.light,
            }} />
            <div>
              <h3 style={{
                margin: 0,
                fontSize: theme.typography.fontSize.lg,
                fontWeight: theme.typography.fontWeight.semibold,
                color: theme.colors.text.primary,
              }}>
                {config?.economist || 'Header'}
              </h3>
              <p style={{
                margin: `${theme.spacing.xs}px 0 0 0`,
                fontSize: theme.typography.fontSize.sm,
                color: theme.colors.text.secondary,
              }}>
                {config?.content || 'Content placeholder'}
              </p>
            </div>
          </div>
        );

      case 'info':
        return (
          <div style={{
            padding: theme.spacing.lg,
            textAlign: 'center',
          }}>
            <h4 style={{
              margin: `0 0 ${theme.spacing.sm}px 0`,
              fontSize: theme.typography.fontSize.md,
              fontWeight: theme.typography.fontWeight.medium,
              color: theme.colors.text.primary,
            }}>
              {config?.content || 'Info Widget'}
            </h4>
            {config?.details && (
              <p style={{
                margin: 0,
                fontSize: theme.typography.fontSize.sm,
                color: theme.colors.text.secondary,
                lineHeight: theme.typography.lineHeight.relaxed,
              }}>
                {config.details}
              </p>
            )}
          </div>
        );

      case 'filter':
        return (
          <div style={{
            display: 'flex',
            gap: theme.spacing.sm,
            padding: theme.spacing.md,
            justifyContent: 'center',
          }}>
            {(config?.tabs || ['Tab 1', 'Tab 2', 'Tab 3']).map((tab: string, index: number) => (
              <button
                key={index}
                style={{
                  padding: `${theme.spacing.sm}px ${theme.spacing.lg}px`,
                  border: 'none',
                  borderRadius: theme.borderRadius.md,
                  backgroundColor: tab === config?.activeTab ? theme.colors.primary : 'transparent',
                  color: tab === config?.activeTab ? 'white' : theme.colors.text.secondary,
                  fontSize: theme.typography.fontSize.sm,
                  fontWeight: theme.typography.fontWeight.medium,
                  cursor: 'pointer',
                  transition: `all ${theme.transitions.fast}`,
                }}
              >
                {tab}
              </button>
            ))}
          </div>
        );

      default:
        return (
          <div style={{
            padding: theme.spacing.xl,
            textAlign: 'center',
            color: theme.colors.text.secondary,
          }}>
            <div style={{
              fontSize: theme.typography.fontSize.sm,
              marginBottom: theme.spacing.sm,
            }}>
              Widget Type: {type}
            </div>
            <div style={{
              fontSize: theme.typography.fontSize.xs,
              opacity: 0.7,
            }}>
              ID: {widgetId}
            </div>
          </div>
        );
    }
  };

  return renderPlaceholder();
};