import React from 'react';
import { theme } from '../../../config/theme.config';
import { WidgetContainer } from '../../layout/WidgetContainer';
import type { WidgetPosition } from '../../../types/dashboard.types';

interface KPIWidgetProps {
  id: string;
  title?: string;
  position: WidgetPosition;
  value: number | string;
  previousValue?: number | string;
  format?: 'number' | 'currency' | 'percentage';
  trend?: 'up' | 'down' | 'neutral';
  suffix?: string;
  prefix?: string;
}

export const KPIWidget: React.FC<KPIWidgetProps> = ({
  id,
  title,
  position,
  value,
  previousValue,
  format = 'number',
  trend,
  suffix,
  prefix,
}) => {
  const formatValue = (val: number | string): string => {
    if (typeof val === 'string') return val;
    
    switch (format) {
      case 'currency':
        if (suffix === 'B') {
          return `${prefix || ''}${(val / 1000000000).toFixed(1)}${suffix}`;
        }
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(val);
      case 'percentage':
        return `${val.toFixed(1)}%`;
      default:
        return new Intl.NumberFormat('en-US').format(val);
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return theme.colors.success;
      case 'down':
        return theme.colors.error;
      default:
        return theme.colors.text.secondary;
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return '↑';
      case 'down':
        return '↓';
      default:
        return '→';
    }
  };

  return (
    <WidgetContainer title={title} position={position}>
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        textAlign: 'center',
      }}>
        <div style={{
          fontSize: '36px',
          fontWeight: 700,
          color: theme.colors.text.primary,
          lineHeight: 1.2,
        }}>
          {prefix}{formatValue(value)}{suffix}
        </div>
        {previousValue !== undefined && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing.xs,
            marginTop: theme.spacing.sm,
            color: getTrendColor(),
            fontSize: '14px',
          }}>
            {trend && <span>{getTrendIcon()}</span>}
            <span>
              {typeof previousValue === 'string' ? previousValue : `${formatValue(previousValue)} previous`}
            </span>
          </div>
        )}
      </div>
    </WidgetContainer>
  );
};