import React, { useState, useEffect } from 'react';
import { theme } from '../../config/theme.config';
import { glassPresets } from '../../config/glass.config';
import { glassUtils } from '../../utils/glassUtils';

interface GlassControlsProps {
  onSettingsChange?: (settings: GlassSettings) => void;
  className?: string;
  style?: React.CSSProperties;
}

export interface GlassSettings {
  enabled: boolean;
  preset: keyof typeof glassPresets;
  intensity: number; // 0-100
  respectMotionPreference: boolean;
  respectPerformance: boolean;
  customSettings?: {
    displacementScale?: number;
    blurAmount?: number;
    saturation?: number;
    opacity?: number;
  };
}

const DEFAULT_SETTINGS: GlassSettings = {
  enabled: true,
  preset: 'medium',
  intensity: 75,
  respectMotionPreference: true,
  respectPerformance: true,
};

/**
 * GlassControls - User interface for controlling glass effects
 * 
 * Provides controls for enabling/disabling glass effects, adjusting intensity,
 * and configuring accessibility preferences.
 */
export const GlassControls: React.FC<GlassControlsProps> = ({
  onSettingsChange,
  className = '',
  style = {},
}) => {
  const [settings, setSettings] = useState<GlassSettings>(() => {
    // Load settings from localStorage if available
    const saved = localStorage.getItem('glass-settings');
    return saved ? { ...DEFAULT_SETTINGS, ...JSON.parse(saved) } : DEFAULT_SETTINGS;
  });

  const [isExpanded, setIsExpanded] = useState(false);
  const [environmentInfo, setEnvironmentInfo] = useState(glassUtils.getEnvironmentInfo());

  // Update environment info when settings change
  useEffect(() => {
    setEnvironmentInfo(glassUtils.getEnvironmentInfo());
  }, [settings]);

  // Save settings to localStorage and notify parent
  useEffect(() => {
    localStorage.setItem('glass-settings', JSON.stringify(settings));
    onSettingsChange?.(settings);
  }, [settings, onSettingsChange]);

  const handleSettingChange = <K extends keyof GlassSettings>(
    key: K,
    value: GlassSettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const resetToDefaults = () => {
    setSettings(DEFAULT_SETTINGS);
  };

  const getRecommendedSettings = () => {
    const shouldEnable = glassUtils.shouldEnableGlassEffects({
      respectPerformance: settings.respectPerformance,
      respectAccessibility: settings.respectMotionPreference,
      respectBrowserSupport: true,
    });

    const recommendedPreset = environmentInfo.device.type === 'mobile' ? 'minimal' :
                             environmentInfo.device.type === 'tablet' ? 'subtle' :
                             'medium';

    return {
      enabled: shouldEnable,
      preset: recommendedPreset as keyof typeof glassPresets,
      intensity: shouldEnable ? 75 : 0,
    };
  };

  const applyRecommendedSettings = () => {
    const recommended = getRecommendedSettings();
    setSettings(prev => ({ ...prev, ...recommended }));
  };

  const controlsStyle: React.CSSProperties = {
    position: 'fixed',
    top: theme.spacing.lg,
    right: theme.spacing.lg,
    zIndex: 1000,
    background: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(10px)',
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    boxShadow: theme.shadows.lg,
    border: `1px solid ${theme.colors.border.light}`,
    minWidth: '280px',
    maxWidth: '320px',
    ...style,
  };

  const buttonStyle: React.CSSProperties = {
    background: theme.colors.primary,
    color: 'white',
    border: 'none',
    borderRadius: theme.borderRadius.md,
    padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    cursor: 'pointer',
    transition: theme.transitions.fast,
  };

  const toggleButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    background: isExpanded ? theme.colors.secondary : theme.colors.primary,
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  };

  const sliderStyle: React.CSSProperties = {
    width: '100%',
    height: '4px',
    borderRadius: '2px',
    background: theme.colors.border.light,
    outline: 'none',
    cursor: 'pointer',
  };

  const checkboxStyle: React.CSSProperties = {
    marginRight: theme.spacing.sm,
    cursor: 'pointer',
  };

  const labelStyle: React.CSSProperties = {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.medium,
    marginBottom: theme.spacing.xs,
    display: 'block',
  };

  const sectionStyle: React.CSSProperties = {
    marginBottom: theme.spacing.md,
    paddingBottom: theme.spacing.md,
    borderBottom: `1px solid ${theme.colors.border.light}`,
  };

  return (
    <div className={`glass-controls ${className}`} style={controlsStyle}>
      {/* Toggle Button */}
      <button
        style={toggleButtonStyle}
        onClick={() => setIsExpanded(!isExpanded)}
        aria-expanded={isExpanded}
        aria-label="Glass Effects Controls"
      >
        <span>✨ Glass Effects</span>
        <span>{isExpanded ? '▼' : '▶'}</span>
      </button>

      {/* Expanded Controls */}
      {isExpanded && (
        <div style={{ marginTop: theme.spacing.md }}>
          {/* Enable/Disable */}
          <div style={sectionStyle}>
            <label style={labelStyle}>
              <input
                type="checkbox"
                checked={settings.enabled}
                onChange={(e) => handleSettingChange('enabled', e.target.checked)}
                style={checkboxStyle}
              />
              Enable Glass Effects
            </label>
          </div>

          {/* Preset Selection */}
          <div style={sectionStyle}>
            <label style={labelStyle}>Preset</label>
            <select
              value={settings.preset}
              onChange={(e) => handleSettingChange('preset', e.target.value as keyof typeof glassPresets)}
              style={{
                width: '100%',
                padding: theme.spacing.sm,
                borderRadius: theme.borderRadius.sm,
                border: `1px solid ${theme.colors.border.light}`,
                fontSize: theme.typography.fontSize.sm,
              }}
              disabled={!settings.enabled}
            >
              {Object.entries(glassPresets).map(([key, preset]) => (
                <option key={key} value={key}>
                  {preset.name} - {preset.description}
                </option>
              ))}
            </select>
          </div>

          {/* Intensity Slider */}
          <div style={sectionStyle}>
            <label style={labelStyle}>
              Intensity: {settings.intensity}%
            </label>
            <input
              type="range"
              min="0"
              max="100"
              value={settings.intensity}
              onChange={(e) => handleSettingChange('intensity', parseInt(e.target.value))}
              style={sliderStyle}
              disabled={!settings.enabled}
            />
          </div>

          {/* Accessibility Options */}
          <div style={sectionStyle}>
            <label style={labelStyle}>Accessibility</label>
            <label style={{ ...labelStyle, fontWeight: 'normal', fontSize: theme.typography.fontSize.xs }}>
              <input
                type="checkbox"
                checked={settings.respectMotionPreference}
                onChange={(e) => handleSettingChange('respectMotionPreference', e.target.checked)}
                style={checkboxStyle}
              />
              Respect reduced motion preference
            </label>
            <label style={{ ...labelStyle, fontWeight: 'normal', fontSize: theme.typography.fontSize.xs }}>
              <input
                type="checkbox"
                checked={settings.respectPerformance}
                onChange={(e) => handleSettingChange('respectPerformance', e.target.checked)}
                style={checkboxStyle}
              />
              Respect device performance
            </label>
          </div>

          {/* Environment Info */}
          <div style={sectionStyle}>
            <label style={labelStyle}>Environment</label>
            <div style={{ fontSize: theme.typography.fontSize.xs, color: theme.colors.text.secondary }}>
              <div>Browser: {environmentInfo.browser.browser}</div>
              <div>Device: {environmentInfo.device.type}</div>
              <div>Support: {environmentInfo.browser.fullGlassSupport ? '✅ Full' : '⚠️ Limited'}</div>
              {environmentInfo.accessibility.reducedMotion && <div>🔄 Reduced motion preferred</div>}
              {environmentInfo.device.isLowPerformance && <div>⚡ Low performance device</div>}
            </div>
          </div>

          {/* Action Buttons */}
          <div style={{ display: 'flex', gap: theme.spacing.sm, flexWrap: 'wrap' }}>
            <button
              style={{ ...buttonStyle, flex: 1, fontSize: theme.typography.fontSize.xs }}
              onClick={applyRecommendedSettings}
            >
              Auto-Configure
            </button>
            <button
              style={{ 
                ...buttonStyle, 
                flex: 1, 
                background: theme.colors.secondary,
                fontSize: theme.typography.fontSize.xs 
              }}
              onClick={resetToDefaults}
            >
              Reset
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Hook for using glass settings throughout the application
 */
export const useGlassSettings = () => {
  const [settings, setSettings] = useState<GlassSettings>(() => {
    const saved = localStorage.getItem('glass-settings');
    return saved ? { ...DEFAULT_SETTINGS, ...JSON.parse(saved) } : DEFAULT_SETTINGS;
  });

  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'glass-settings' && e.newValue) {
        setSettings({ ...DEFAULT_SETTINGS, ...JSON.parse(e.newValue) });
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const updateSettings = (newSettings: Partial<GlassSettings>) => {
    const updated = { ...settings, ...newSettings };
    setSettings(updated);
    localStorage.setItem('glass-settings', JSON.stringify(updated));
  };

  return {
    settings,
    updateSettings,
    isEnabled: settings.enabled,
    effectivePreset: settings.enabled ? settings.preset : 'minimal',
    effectiveIntensity: settings.enabled ? settings.intensity : 0,
  };
};
