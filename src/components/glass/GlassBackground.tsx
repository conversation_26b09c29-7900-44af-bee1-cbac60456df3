import React, { useMemo } from 'react';
import { theme } from '../../config/theme.config';

interface GlassBackgroundProps {
  variant?: 'subtle' | 'gradient' | 'mesh' | 'dots' | 'waves';
  opacity?: number;
  animated?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * GlassBackground - Enhanced background component for glass effects
 * 
 * Provides various background patterns and gradients that work well
 * with glass effects, creating depth and visual interest while
 * maintaining professional appearance.
 */
export const GlassBackground: React.FC<GlassBackgroundProps> = ({
  variant = 'gradient',
  opacity = 0.6,
  animated = true,
  className = '',
  style = {},
}) => {
  const backgroundStyles = useMemo((): React.CSSProperties => {
    const baseStyles: React.CSSProperties = {
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: -1,
      opacity,
      pointerEvents: 'none',
      ...style,
    };

    switch (variant) {
      case 'subtle':
        return {
          ...baseStyles,
          background: theme.colors.backgroundGradient,
        };

      case 'gradient':
        return {
          ...baseStyles,
          background: `
            linear-gradient(135deg, 
              rgba(37, 99, 235, 0.1) 0%, 
              rgba(59, 130, 246, 0.05) 25%,
              rgba(6, 182, 212, 0.08) 50%,
              rgba(8, 145, 178, 0.03) 75%,
              rgba(37, 99, 235, 0.06) 100%
            ),
            ${theme.colors.backgroundGradient}
          `,
        };

      case 'mesh':
        return {
          ...baseStyles,
          background: `
            radial-gradient(circle at 20% 20%, rgba(37, 99, 235, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(6, 182, 212, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
            ${theme.colors.backgroundGradient}
          `,
          animation: animated ? 'meshFloat 20s ease-in-out infinite' : 'none',
        };

      case 'dots':
        return {
          ...baseStyles,
          background: `
            radial-gradient(circle, rgba(37, 99, 235, 0.1) 1px, transparent 1px),
            ${theme.colors.backgroundGradient}
          `,
          backgroundSize: '40px 40px',
          animation: animated ? 'dotsMove 30s linear infinite' : 'none',
        };

      case 'waves':
        return {
          ...baseStyles,
          background: `
            linear-gradient(135deg, 
              rgba(37, 99, 235, 0.08) 0%, 
              transparent 25%,
              rgba(6, 182, 212, 0.06) 50%,
              transparent 75%,
              rgba(59, 130, 246, 0.04) 100%
            ),
            ${theme.colors.backgroundGradient}
          `,
          backgroundSize: '200% 200%',
          animation: animated ? 'waveMove 15s ease-in-out infinite' : 'none',
        };

      default:
        return baseStyles;
    }
  }, [variant, opacity, animated, style]);

  return (
    <div 
      className={`glass-background glass-background--${variant} ${className}`}
      style={backgroundStyles}
    />
  );
};

// CSS animations for background effects
const backgroundAnimations = `
  @keyframes meshFloat {
    0%, 100% {
      background-position: 0% 0%, 100% 100%, 50% 50%;
    }
    33% {
      background-position: 30% 30%, 70% 70%, 80% 20%;
    }
    66% {
      background-position: 70% 10%, 30% 90%, 20% 80%;
    }
  }

  @keyframes dotsMove {
    0% {
      background-position: 0px 0px;
    }
    100% {
      background-position: 40px 40px;
    }
  }

  @keyframes waveMove {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.8;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .glass-background {
      animation: none !important;
    }
  }
`;

// Inject animations into document head
if (typeof document !== 'undefined' && !document.querySelector('#glass-background-animations')) {
  const style = document.createElement('style');
  style.id = 'glass-background-animations';
  style.textContent = backgroundAnimations;
  document.head.appendChild(style);
}

/**
 * GlassOrb - Floating orb component for additional visual interest
 */
interface GlassOrbProps {
  size?: number;
  color?: string;
  x?: number;
  y?: number;
  animated?: boolean;
  blur?: number;
}

export const GlassOrb: React.FC<GlassOrbProps> = ({
  size = 200,
  color = 'rgba(37, 99, 235, 0.1)',
  x = 50,
  y = 50,
  animated = true,
  blur = 40,
}) => {
  const orbStyles: React.CSSProperties = {
    position: 'absolute',
    width: `${size}px`,
    height: `${size}px`,
    borderRadius: '50%',
    background: `radial-gradient(circle, ${color} 0%, transparent 70%)`,
    filter: `blur(${blur}px)`,
    left: `${x}%`,
    top: `${y}%`,
    transform: 'translate(-50%, -50%)',
    pointerEvents: 'none',
    animation: animated ? 'float 6s ease-in-out infinite' : 'none',
  };

  return <div style={orbStyles} className="glass-orb" />;
};

/**
 * GlassBackgroundWithOrbs - Background with floating orbs
 */
interface GlassBackgroundWithOrbsProps extends GlassBackgroundProps {
  orbCount?: number;
  orbSizes?: number[];
  orbColors?: string[];
}

export const GlassBackgroundWithOrbs: React.FC<GlassBackgroundWithOrbsProps> = ({
  orbCount = 3,
  orbSizes = [150, 200, 180],
  orbColors = [
    'rgba(37, 99, 235, 0.08)',
    'rgba(6, 182, 212, 0.06)',
    'rgba(59, 130, 246, 0.04)',
  ],
  ...backgroundProps
}) => {
  const orbs = useMemo(() => {
    return Array.from({ length: orbCount }, (_, index) => ({
      id: index,
      size: orbSizes[index % orbSizes.length],
      color: orbColors[index % orbColors.length],
      x: 20 + (index * 30) % 60,
      y: 15 + (index * 25) % 70,
    }));
  }, [orbCount, orbSizes, orbColors]);

  return (
    <div style={{ position: 'relative', width: '100%', height: '100%' }}>
      <GlassBackground {...backgroundProps} />
      {orbs.map((orb) => (
        <GlassOrb
          key={orb.id}
          size={orb.size}
          color={orb.color}
          x={orb.x}
          y={orb.y}
          animated={backgroundProps.animated}
        />
      ))}
    </div>
  );
};
