import React, { forwardRef, useRef, useEffect, useState } from 'react';
import { useGlassEffects, useGlassAnimations } from '../../hooks/useGlassEffects';
import { useIntersectionObserver, useCSSContainment } from '../../hooks/usePerformanceOptimization';
import { glassPresets, type GlassEffectConfig } from '../../config/glass.config';
import { theme } from '../../config/theme.config';

interface GlassContainerProps {
  children: React.ReactNode;
  preset?: keyof typeof glassPresets;
  className?: string;
  style?: React.CSSProperties;
  disabled?: boolean;
  respectMotionPreference?: boolean;
  customConfig?: Partial<GlassEffectConfig>;
  onClick?: () => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
  tabIndex?: number;
  role?: string;
  'aria-label'?: string;
  'data-testid'?: string;
  enableHoverEffects?: boolean;
  enableFocusEffects?: boolean;
  enableClickEffects?: boolean;
  mouseContainer?: React.RefObject<HTMLElement>;
  lazyLoad?: boolean;
  enableContainment?: boolean;
}

/**
 * GlassContainer - Core component for applying liquid glass effects
 * 
 * This component provides a glassmorphism effect with configurable presets,
 * browser detection, accessibility support, and responsive behavior.
 */
export const GlassContainer = forwardRef<HTMLDivElement, GlassContainerProps>(({
  children,
  preset = 'medium',
  className = '',
  style = {},
  disabled = false,
  respectMotionPreference = true,
  customConfig = {},
  onClick,
  onMouseEnter,
  onMouseLeave,
  onFocus,
  onBlur,
  tabIndex,
  role,
  'aria-label': ariaLabel,
  'data-testid': dataTestId,
  enableHoverEffects = true,
  enableFocusEffects = true,
  enableClickEffects = true,
  mouseContainer,
  lazyLoad = true,
  enableContainment = true,
}, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isMouseInside, setIsMouseInside] = useState(false);

  // Performance optimizations
  const { targetRef: intersectionRef, isIntersecting, hasIntersected } = useIntersectionObserver();
  const containmentRef = useCSSContainment(enableContainment ? 'layout style paint' : undefined);

  // Only enable effects if in viewport (when lazy loading is enabled)
  const shouldRenderEffects = !lazyLoad || hasIntersected;

  // Combine refs
  const combinedRef = (node: HTMLDivElement) => {
    containerRef.current = node;
    if (typeof ref === 'function') {
      ref(node);
    } else if (ref) {
      ref.current = node;
    }
  };

  const {
    glassConfig,
    isSupported,
    shouldUseReducedEffects,
    glassStyles,
    hoverStyles,
    focusStyles,
  } = useGlassEffects({
    preset,
    disabled,
    respectMotionPreference,
    customConfig,
  });

  const {
    isHovered,
    isFocused,
    isActive,
    handlers,
  } = useGlassAnimations(!shouldUseReducedEffects);

  // Handle mouse movement for liquid effect
  useEffect(() => {
    if (shouldUseReducedEffects || !isSupported) return;

    const targetElement = mouseContainer?.current || containerRef.current;
    if (!targetElement) return;

    const handleMouseMove = (e: MouseEvent) => {
      const rect = targetElement.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;
      
      setMousePosition({ x, y });
    };

    const handleMouseEnter = () => {
      setIsMouseInside(true);
    };

    const handleMouseLeave = () => {
      setIsMouseInside(false);
    };

    targetElement.addEventListener('mousemove', handleMouseMove);
    targetElement.addEventListener('mouseenter', handleMouseEnter);
    targetElement.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      targetElement.removeEventListener('mousemove', handleMouseMove);
      targetElement.removeEventListener('mouseenter', handleMouseEnter);
      targetElement.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [shouldUseReducedEffects, isSupported, mouseContainer]);

  // Calculate dynamic styles based on mouse position and state
  const dynamicStyles: React.CSSProperties = {
    ...glassStyles,
    ...style,
    transition: shouldUseReducedEffects 
      ? theme.transitions.normal 
      : `all ${theme.transitions.fast}, transform ${theme.transitions.bounce}`,
  };

  // Apply hover effects
  if (enableHoverEffects && isHovered && !shouldUseReducedEffects) {
    Object.assign(dynamicStyles, hoverStyles);
  }

  // Apply focus effects
  if (enableFocusEffects && isFocused) {
    Object.assign(dynamicStyles, focusStyles);
  }

  // Apply active/click effects
  if (enableClickEffects && isActive && !shouldUseReducedEffects) {
    dynamicStyles.transform = 'translateY(0px) scale(0.98)';
  }

  // Add liquid glass displacement effect
  if (isSupported && !shouldUseReducedEffects && isMouseInside) {
    const elasticity = glassConfig.elasticity;
    const displacement = glassConfig.displacementScale;
    
    // Calculate transform based on mouse position
    const xOffset = (mousePosition.x - 50) * elasticity * 0.1;
    const yOffset = (mousePosition.y - 50) * elasticity * 0.1;
    
    const currentTransform = dynamicStyles.transform || '';
    dynamicStyles.transform = `${currentTransform} translate(${xOffset}px, ${yOffset}px)`;
    
    // Add subtle rotation based on mouse position
    const rotation = (mousePosition.x - 50) * elasticity * 0.02;
    dynamicStyles.transform += ` rotate(${rotation}deg)`;
  }

  // Add glass-specific pseudo-element styles via CSS variables
  const cssVariables = {
    '--glass-bg': glassConfig.background,
    '--glass-border': glassConfig.border,
    '--glass-blur': glassConfig.backdropBlur,
    '--glass-shadow': glassConfig.boxShadow,
    '--glass-opacity': glassConfig.opacity,
    '--mouse-x': `${mousePosition.x}%`,
    '--mouse-y': `${mousePosition.y}%`,
  } as React.CSSProperties;

  const combinedHandlers = {
    onClick: (e: React.MouseEvent<HTMLDivElement>) => {
      onClick?.();
      handlers.onMouseDown();
      setTimeout(handlers.onMouseUp, 150);
    },
    onMouseEnter: (e: React.MouseEvent<HTMLDivElement>) => {
      onMouseEnter?.();
      if (enableHoverEffects) handlers.onMouseEnter();
    },
    onMouseLeave: (e: React.MouseEvent<HTMLDivElement>) => {
      onMouseLeave?.();
      if (enableHoverEffects) handlers.onMouseLeave();
    },
    onFocus: (e: React.FocusEvent<HTMLDivElement>) => {
      onFocus?.();
      if (enableFocusEffects) handlers.onFocus();
    },
    onBlur: (e: React.FocusEvent<HTMLDivElement>) => {
      onBlur?.();
      if (enableFocusEffects) handlers.onBlur();
    },
  };

  return (
    <div
      ref={combinedRef}
      className={`glass-container ${className}`}
      style={{
        ...cssVariables,
        ...dynamicStyles,
      }}
      tabIndex={tabIndex}
      role={role}
      aria-label={ariaLabel}
      data-testid={dataTestId}
      data-glass-preset={preset}
      data-glass-supported={isSupported}
      data-reduced-effects={shouldUseReducedEffects}
      {...combinedHandlers}
    >
      {/* Glass highlight overlay */}
      {isSupported && !shouldUseReducedEffects && (
        <div
          className="glass-highlight"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '1px',
            background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent)',
            opacity: isHovered ? 1 : 0.6,
            transition: theme.transitions.fast,
            pointerEvents: 'none',
          }}
        />
      )}
      
      {/* Glass edge glow */}
      {isSupported && !shouldUseReducedEffects && isHovered && (
        <div
          className="glass-glow"
          style={{
            position: 'absolute',
            top: -2,
            left: -2,
            right: -2,
            bottom: -2,
            background: `linear-gradient(135deg, 
              rgba(255, 255, 255, 0.1) 0%, 
              rgba(255, 255, 255, 0.05) 50%, 
              rgba(255, 255, 255, 0.1) 100%)`,
            borderRadius: `${glassConfig.cornerRadius + 2}px`,
            opacity: 0.5,
            filter: 'blur(1px)',
            pointerEvents: 'none',
            zIndex: -1,
          }}
        />
      )}

      {/* Content */}
      <div
        className="glass-content"
        style={{
          position: 'relative',
          zIndex: 1,
          height: '100%',
          width: '100%',
        }}
      >
        {children}
      </div>
    </div>
  );
});

GlassContainer.displayName = 'GlassContainer';
