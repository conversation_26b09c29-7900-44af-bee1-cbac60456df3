import React from 'react';
import { theme } from '../../config/theme.config';

/**
 * GlassPatterns - Collection of background patterns optimized for glass effects
 * 
 * Provides various subtle background patterns that enhance glass refraction
 * while maintaining professional appearance and data readability.
 */

interface PatternProps {
  opacity?: number;
  color?: string;
  size?: number;
  animated?: boolean;
}

/**
 * Subtle dot pattern for glass backgrounds
 */
export const DotPattern: React.FC<PatternProps> = ({
  opacity = 0.1,
  color = '#2563eb',
  size = 2,
  animated = false,
}) => {
  const patternId = `dot-pattern-${Math.random().toString(36).substr(2, 9)}`;
  
  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity,
        pointerEvents: 'none',
        zIndex: -1,
      }}
    >
      <svg width="100%" height="100%" style={{ position: 'absolute' }}>
        <defs>
          <pattern
            id={patternId}
            x="0"
            y="0"
            width="40"
            height="40"
            patternUnits="userSpaceOnUse"
          >
            <circle
              cx="20"
              cy="20"
              r={size}
              fill={color}
              opacity={0.6}
            />
          </pattern>
        </defs>
        <rect
          width="100%"
          height="100%"
          fill={`url(#${patternId})`}
          style={{
            animation: animated ? 'patternMove 30s linear infinite' : 'none',
          }}
        />
      </svg>
    </div>
  );
};

/**
 * Grid pattern for glass backgrounds
 */
export const GridPattern: React.FC<PatternProps> = ({
  opacity = 0.05,
  color = '#2563eb',
  size = 1,
  animated = false,
}) => {
  const patternId = `grid-pattern-${Math.random().toString(36).substr(2, 9)}`;
  
  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity,
        pointerEvents: 'none',
        zIndex: -1,
      }}
    >
      <svg width="100%" height="100%" style={{ position: 'absolute' }}>
        <defs>
          <pattern
            id={patternId}
            x="0"
            y="0"
            width="60"
            height="60"
            patternUnits="userSpaceOnUse"
          >
            <path
              d="M 60 0 L 0 0 0 60"
              fill="none"
              stroke={color}
              strokeWidth={size}
              opacity={0.3}
            />
          </pattern>
        </defs>
        <rect
          width="100%"
          height="100%"
          fill={`url(#${patternId})`}
          style={{
            animation: animated ? 'patternSlide 25s linear infinite' : 'none',
          }}
        />
      </svg>
    </div>
  );
};

/**
 * Hexagon pattern for glass backgrounds
 */
export const HexPattern: React.FC<PatternProps> = ({
  opacity = 0.08,
  color = '#2563eb',
  size = 30,
  animated = false,
}) => {
  const patternId = `hex-pattern-${Math.random().toString(36).substr(2, 9)}`;
  
  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity,
        pointerEvents: 'none',
        zIndex: -1,
      }}
    >
      <svg width="100%" height="100%" style={{ position: 'absolute' }}>
        <defs>
          <pattern
            id={patternId}
            x="0"
            y="0"
            width={size * 2}
            height={size * 1.732}
            patternUnits="userSpaceOnUse"
          >
            <polygon
              points={`${size},0 ${size * 1.5},${size * 0.433} ${size * 1.5},${size * 1.299} ${size},${size * 1.732} ${size * 0.5},${size * 1.299} ${size * 0.5},${size * 0.433}`}
              fill="none"
              stroke={color}
              strokeWidth="1"
              opacity={0.4}
            />
          </pattern>
        </defs>
        <rect
          width="100%"
          height="100%"
          fill={`url(#${patternId})`}
          style={{
            animation: animated ? 'patternRotate 40s linear infinite' : 'none',
          }}
        />
      </svg>
    </div>
  );
};

/**
 * Wave pattern for glass backgrounds
 */
export const WavePattern: React.FC<PatternProps> = ({
  opacity = 0.06,
  color = '#2563eb',
  size = 40,
  animated = true,
}) => {
  const patternId = `wave-pattern-${Math.random().toString(36).substr(2, 9)}`;
  
  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity,
        pointerEvents: 'none',
        zIndex: -1,
      }}
    >
      <svg width="100%" height="100%" style={{ position: 'absolute' }}>
        <defs>
          <pattern
            id={patternId}
            x="0"
            y="0"
            width={size * 4}
            height={size}
            patternUnits="userSpaceOnUse"
          >
            <path
              d={`M0,${size/2} Q${size},0 ${size*2},${size/2} T${size*4},${size/2}`}
              fill="none"
              stroke={color}
              strokeWidth="2"
              opacity={0.5}
            />
          </pattern>
        </defs>
        <rect
          width="100%"
          height="100%"
          fill={`url(#${patternId})`}
          style={{
            animation: animated ? 'waveFlow 20s ease-in-out infinite' : 'none',
          }}
        />
      </svg>
    </div>
  );
};

/**
 * Noise pattern for subtle texture
 */
export const NoisePattern: React.FC<PatternProps> = ({
  opacity = 0.03,
  size = 100,
}) => {
  const patternId = `noise-pattern-${Math.random().toString(36).substr(2, 9)}`;
  
  // Generate random noise points
  const noisePoints = Array.from({ length: 50 }, (_, i) => ({
    x: Math.random() * size,
    y: Math.random() * size,
    r: Math.random() * 1.5 + 0.5,
  }));
  
  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity,
        pointerEvents: 'none',
        zIndex: -1,
      }}
    >
      <svg width="100%" height="100%" style={{ position: 'absolute' }}>
        <defs>
          <pattern
            id={patternId}
            x="0"
            y="0"
            width={size}
            height={size}
            patternUnits="userSpaceOnUse"
          >
            {noisePoints.map((point, index) => (
              <circle
                key={index}
                cx={point.x}
                cy={point.y}
                r={point.r}
                fill="#000"
                opacity={0.1}
              />
            ))}
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill={`url(#${patternId})`} />
      </svg>
    </div>
  );
};

/**
 * Combined pattern component with multiple layers
 */
interface GlassPatternBackgroundProps {
  variant?: 'minimal' | 'subtle' | 'rich';
  animated?: boolean;
  className?: string;
}

export const GlassPatternBackground: React.FC<GlassPatternBackgroundProps> = ({
  variant = 'subtle',
  animated = true,
  className = '',
}) => {
  const patterns = {
    minimal: [
      <NoisePattern key="noise" opacity={0.02} />,
    ],
    subtle: [
      <NoisePattern key="noise" opacity={0.03} />,
      <DotPattern key="dots" opacity={0.05} animated={animated} />,
    ],
    rich: [
      <NoisePattern key="noise" opacity={0.04} />,
      <DotPattern key="dots" opacity={0.06} animated={animated} />,
      <GridPattern key="grid" opacity={0.03} animated={animated} />,
      <WavePattern key="waves" opacity={0.04} animated={animated} />,
    ],
  };

  return (
    <div className={`glass-pattern-background ${className}`}>
      {patterns[variant]}
    </div>
  );
};

// CSS animations for patterns
const patternAnimations = `
  @keyframes patternMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(40px, 40px); }
  }

  @keyframes patternSlide {
    0% { transform: translateX(0); }
    100% { transform: translateX(60px); }
  }

  @keyframes patternRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes waveFlow {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(-40px); }
  }

  @media (prefers-reduced-motion: reduce) {
    .glass-pattern-background * {
      animation: none !important;
    }
  }
`;

// Inject animations into document head
if (typeof document !== 'undefined' && !document.querySelector('#glass-pattern-animations')) {
  const style = document.createElement('style');
  style.id = 'glass-pattern-animations';
  style.textContent = patternAnimations;
  document.head.appendChild(style);
}
