import React from 'react';
import { GlassContainer } from './GlassContainer';
import { theme } from '../../config/theme.config';
import { widgetGlassConfig, glassPresets } from '../../config/glass.config';
import type { WidgetPosition } from '../../types/dashboard.types';

interface GlassWidgetContainerProps {
  children: React.ReactNode;
  title?: string;
  position: WidgetPosition;
  loading?: boolean;
  error?: Error | null;
  widgetType?: keyof typeof widgetGlassConfig;
  glassPreset?: keyof typeof glassPresets;
  enableGlassEffects?: boolean;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  'data-testid'?: string;
}

/**
 * GlassWidgetContainer - Enhanced widget container with glass effects
 * 
 * This component combines the functionality of the original WidgetContainer
 * with configurable glass effects, maintaining all existing features while
 * adding modern glassmorphism styling.
 */
export const GlassWidgetContainer: React.FC<GlassWidgetContainerProps> = ({
  children,
  title,
  position,
  loading = false,
  error = null,
  widgetType = 'chart',
  glassPreset,
  enableGlassEffects = true,
  className = '',
  style = {},
  onClick,
  'data-testid': dataTestId,
}) => {
  // Determine glass preset based on widget type if not explicitly provided
  const effectivePreset = glassPreset || widgetGlassConfig[widgetType];

  // Grid positioning styles
  const gridStyles = {
    gridColumn: `span ${position.w}`,
    gridRow: `span ${position.h}`,
  };

  // Base container styles (maintaining original WidgetContainer behavior)
  const containerStyles: React.CSSProperties = {
    ...gridStyles,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    minHeight: `${position.h * 120}px`,
    height: 'auto',
    position: 'relative',
    ...style,
  };

  // Enhanced styles for glass effect
  const glassContainerStyles: React.CSSProperties = {
    padding: theme.spacing.lg,
    height: '100%',
    width: '100%',
  };

  // Fallback styles when glass effects are disabled
  const fallbackStyles: React.CSSProperties = {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    boxShadow: theme.shadows.sm,
    border: `1px solid ${theme.colors.border.light}`,
    transition: `all ${theme.transitions.fast}`,
  };

  const titleStyles: React.CSSProperties = {
    margin: 0,
    marginBottom: theme.spacing.md,
    color: theme.colors.text.primary,
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    fontFamily: theme.typography.fontFamily.sans,
    lineHeight: theme.typography.lineHeight.tight,
  };

  const contentStyles: React.CSSProperties = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: loading || error ? 'center' : 'stretch',
    alignItems: loading || error ? 'center' : 'stretch',
    minHeight: 0,
    width: '100%',
  };

  const loadingStyles: React.CSSProperties = {
    color: theme.colors.text.secondary,
    fontSize: theme.typography.fontSize.sm,
    textAlign: 'center',
  };

  const errorStyles: React.CSSProperties = {
    color: theme.colors.error,
    textAlign: 'center',
    fontSize: theme.typography.fontSize.sm,
    padding: theme.spacing.md,
    background: 'rgba(239, 68, 68, 0.1)',
    borderRadius: theme.borderRadius.sm,
    border: '1px solid rgba(239, 68, 68, 0.2)',
  };

  // Content to render inside the container
  const containerContent = (
    <>
      {title && (
        <h3 style={titleStyles}>
          {title}
        </h3>
      )}
      
      <div style={contentStyles}>
        {loading && (
          <div style={loadingStyles}>
            <div style={{
              display: 'inline-block',
              width: '20px',
              height: '20px',
              border: '2px solid #f3f3f3',
              borderTop: '2px solid #3498db',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              marginBottom: theme.spacing.sm,
            }} />
            <div>Loading widget...</div>
          </div>
        )}
        
        {error && (
          <div style={errorStyles}>
            <strong>Error loading widget</strong>
            <br />
            {error.message}
          </div>
        )}
        
        {!loading && !error && children}
      </div>
    </>
  );

  // Render with glass effects if enabled
  if (enableGlassEffects) {
    return (
      <div 
        style={containerStyles} 
        className={className}
        data-testid={dataTestId}
        data-widget-type={widgetType}
      >
        <GlassContainer
          preset={effectivePreset}
          style={glassContainerStyles}
          onClick={onClick}
          enableHoverEffects={!loading && !error}
          enableFocusEffects={!!onClick}
          enableClickEffects={!!onClick}
          tabIndex={onClick ? 0 : undefined}
          role={onClick ? 'button' : undefined}
          aria-label={onClick ? `${title || 'Widget'} - Click to interact` : undefined}
          className="glass-widget-container"
        >
          {containerContent}
        </GlassContainer>
      </div>
    );
  }

  // Fallback rendering without glass effects (maintains original behavior)
  return (
    <div
      style={{
        ...containerStyles,
        ...fallbackStyles,
        ...glassContainerStyles,
      }}
      className={`widget-container ${className}`}
      onClick={onClick}
      tabIndex={onClick ? 0 : undefined}
      role={onClick ? 'button' : undefined}
      aria-label={onClick ? `${title || 'Widget'} - Click to interact` : undefined}
      data-testid={dataTestId}
      data-widget-type={widgetType}
      onMouseEnter={(e) => {
        if (!loading && !error) {
          e.currentTarget.style.boxShadow = theme.shadows.md;
          e.currentTarget.style.transform = 'translateY(-2px)';
        }
      }}
      onMouseLeave={(e) => {
        if (!loading && !error) {
          e.currentTarget.style.boxShadow = theme.shadows.sm;
          e.currentTarget.style.transform = 'translateY(0)';
        }
      }}
    >
      {containerContent}
    </div>
  );
};

// Add CSS animation for loading spinner
const spinKeyframes = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// Inject the keyframes into the document head if not already present
if (typeof document !== 'undefined' && !document.querySelector('#glass-widget-animations')) {
  const style = document.createElement('style');
  style.id = 'glass-widget-animations';
  style.textContent = spinKeyframes;
  document.head.appendChild(style);
}
