// Glass Components
export { GlassContainer } from './GlassContainer';
export { GlassWidgetContainer } from './GlassWidgetContainer';
export { GlassBackground, GlassOrb, GlassBackgroundWithOrbs } from './GlassBackground';
export { GlassControls, useGlassSettings } from './GlassControls';
export type { GlassSettings } from './GlassControls';

// Glass Patterns
export {
  DotPattern,
  GridPattern,
  HexPattern,
  WavePattern,
  NoisePattern,
  GlassPatternBackground
} from './GlassPatterns';

// Glass Configuration
export { glassPresets, widgetGlassConfig, browserSupport, responsiveGlass } from '../../config/glass.config';
export type { GlassEffectConfig, GlassPreset } from '../../config/glass.config';

// Glass Hooks
export { useGlassEffects, useGlassAnimations } from '../../hooks/useGlassEffects';
export {
  usePerformanceOptimization,
  useIntersectionObserver,
  useDebouncedResize,
  useMemoryMonitor,
  useFPSMonitor,
  useGlassPerformanceMonitor,
  useCSSContainment,
  useOptimizedAnimation
} from '../../hooks/usePerformanceOptimization';

// Glass Utilities
export { glassUtils } from '../../utils/glassUtils';
export { glassTestSuite, visualTestUtils, performanceMonitor, automatedTests } from '../../utils/glassTestUtils';
