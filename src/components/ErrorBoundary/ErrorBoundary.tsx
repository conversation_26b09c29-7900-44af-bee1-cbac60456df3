import React, { Component, ErrorInfo, ReactNode } from 'react';
import { theme } from '../../config/theme.config';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const errorMessage = this.state.error?.message || '';
      let userFriendlyMessage = 'This widget requires a connection to Sisense.';
      
      // Provide more specific error messages
      if (errorMessage.includes('dimension') && errorMessage.includes('not found')) {
        userFriendlyMessage = 'The data model referenced by this widget is not available in your Sisense instance.';
      } else if (errorMessage.includes('CRS')) {
        userFriendlyMessage = 'This widget requires geographic/mapping features that are not configured.';
      } else if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
        userFriendlyMessage = 'Authentication failed. Please check your Sisense API token.';
      } else if (errorMessage.includes('404')) {
        userFriendlyMessage = 'Widget or dashboard not found. Please verify the IDs are correct.';
      }

      return (
        <div
          style={{
            padding: theme.spacing.lg,
            backgroundColor: theme.colors.surface,
            borderRadius: theme.borderRadius.card,
            border: `1px solid ${theme.colors.border}`,
            textAlign: 'center',
          }}
        >
          <div style={{ fontSize: '36px', marginBottom: theme.spacing.sm }}>⚠️</div>
          <h3 style={{ color: theme.colors.text.primary, marginBottom: theme.spacing.md }}>
            Widget Loading Error
          </h3>
          <p style={{ color: theme.colors.text.secondary, marginBottom: theme.spacing.sm }}>
            {userFriendlyMessage}
          </p>
          {!import.meta.env.VITE_INSTANCE_URL && (
            <p style={{ color: theme.colors.text.secondary, fontSize: '14px' }}>
              Please configure your Sisense instance URL and API token in the .env file.
            </p>
          )}
          {this.state.error && (
            <details style={{ marginTop: theme.spacing.md, textAlign: 'left' }}>
              <summary style={{ cursor: 'pointer', color: theme.colors.text.secondary }}>
                Technical Details
              </summary>
              <pre
                style={{
                  marginTop: theme.spacing.sm,
                  padding: theme.spacing.md,
                  backgroundColor: theme.colors.background,
                  borderRadius: theme.borderRadius.sm,
                  fontSize: '12px',
                  overflow: 'auto',
                  maxHeight: '200px',
                }}
              >
                {this.state.error.message}
              </pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}